ylogdebug_0  on 07-26 10:40:27


uptime on Sat Jul 26 10:40:27 CST 2025
 10:40:27 up 1 day, 12:51,  0 users,  load average: 15.16, 15.03, 15.13
run finished on 07-26 10:40:27


logcat -S on Sat Jul 26 10:40:28 CST 2025
size/num main               system             crash              kernel             Total
Total    128027494/958535   27658012/145386    0/0                70573193/663687    226258699/1767608
Now      827128/6056        1000555/4022       0/0                972403/9469        2800086/19547
Logspan  5:48.931           7:15.04                               9:56.709           9:57.187
Overhead 258951             259469                                255318             981818

Chattiest UIDs in main log buffer:                           Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                               366857
  PID/UID   COMMAND LINE                                       "
 1240/1000  system_server                                  162743
  709/1000  /system/bin/surfaceflinger                     116894
  617/1000 ...ndroid.hardware.graphics.composer@2.4-service 16272
  378/1000  /system/bin/hw/android.system.suspend-service   13640
  685/1000 /vendor/bin/hw/vendor.sprd.hardware.tool-service 12413
  616/1000 ...droid.hardware.graphics.allocator@4.0-service 12147
  642/1000 .../bin/hw/vendor.sprd.hardware.cplog_svc-service 8207
  887/1000  /system_ext/bin/slogmodem                        6472
  935/1000  /vendor/bin/thermald                             5444
  914/1000  /vendor/bin/refnotify                            4655
10184 com.android.systemui                                  89287
0     root                                                  88002
1041  audioserver                                           66934
10120 com.sprd.logmanager                                   57490
10180 com.android.launcher3                                 45033
1001  radio                                                 27185
1027  nfc                                                   24965
10192 com.android.soundrecorder                             14343
10138 com.google.android.gms                                12054
10145 com.google.android.googlequicksearchbox                8068
1073  network_stack                                          5958
10158 com.google.android.inputmethod.latin                   5747
10207 com.google.android.rkpdapp                             4933
10222 com.google.android.cellbroadcastreceiver               3732


Chattiest UIDs in system log buffer:                         Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                               914252
10184 com.android.systemui                                  79211

run finished on 07-26 10:40:28


ylogctl q on Sat Jul 26 10:40:28 CST 2025
status = enable 
file = /data/ylog/ap/000-0726_104027.ylog 
size = 733033 
pid =  523  
[ 12300]  lastlog      -> Open    -> lastlog.log      [     29]->[      4] 
[ 12295]  uboot        -> Open    -> uboot.log        [    100]->[     40] 
[ 12303]  android      -> Open    -> android.log      [     DB]->[      4] 
[ 12302]  kernel       -> Open    -> kernel.log       [    124]->[      7] 
[ 12282]  trace        -> Open    -> trace.log        [      0]->[      0] 
[ 12284]  sgm          -> Open    -> sgm.csv          [      2]->[      2] 
[ 12285]  sysinfo      -> Open    -> sysinfo.log      [      D]->[      5] 
[     0]  thermal      -> Close   -> thermal.log      [      0]->[      0] 
[ 12286]  ylogdebug    -> Open    -> ylogdebug.log    [      B]->[      6] 
[ 12288]  phoneinfo    -> Open    -> phoneinfo.log    [     14]->[      6] 
[ 12308]  hcidump      -> Open    -> hcidump.cap      [      0]->[      0] 
[ 12312]  tcpdump      -> Open    -> tcpdump.cap      [      0]->[      0] 
[ 12292]  trustlog     -> Open    -> trusty.log       [      2]->[      1] 

run finished on 07-26 10:40:28


ylogctl space on Sat Jul 26 10:40:29 CST 2025
Root:/data/ylog/ap/   APLogSize:0 APLogMaxSize:46570 DiskFreeSpace:40657  DiskReserved:60
run finished on 07-26 10:40:29


cat /data/ylog/ylog.conf on Sat Jul 26 10:40:29 CST 2025
VERSION,1
status,enable
aplogrotate,enable
prioritypath,internal
sroot,default
aplogfilesize,256
aplogmaxsize,99%
uboot,1
lastlog,1
kernel,1
android,1
hcidump,1
tcpdump,1
sgm,1
sysinfo,1
thermal,0
ylogdebug,1
phoneinfo,1
trace,1
trustlog,1
tcpdump_c,-s 3000
sublog,1


run finished on 07-26 10:40:29


ls -l /data/ylog/ap/ on Sat Jul 26 10:40:29 CST 2025
total 1251
-rw-rw-rw- 1 <USER>   <GROUP> 1257321 2025-07-26 10:40 000-0726_104027.ylog
-rw-rw-rw- 1 <USER>   <GROUP>   19006 2025-07-26 10:40 analyzer.py
drwxrwxrwx 2 <USER> <GROUP>    3452 2025-07-26 10:40 tcpdump
run finished on 07-26 10:40:30


cat /data/ylog/journal.log on Sat Jul 26 10:40:30 CST 2025
[01-01 08:22:51.240] (     1) [    13]     523     523  

.............................
[01-01 08:22:51.244] (     2) [    13]     523     523  [1] ylog()  start enable:0  ver:5.0.0   bootmode:normal lite:0 sdk:35 plat:
[01-01 08:22:51.248] (     3) [    13]     523     523  /data/ylog/ylog.conf error,reinit it
[01-01 08:22:51.249] (     4) [    13]     523     523  ylog config is /product/etc/ylog.conf.debug
[01-01 08:22:51.257] (     5) [    13]     523     523  syncLegcyConfig
[01-01 08:22:51.263] (     6) [    13]     523     523  startlogServcie LogEnable : 1
[01-01 08:22:51.263] (     7) [    13]     523     523  __ERROR  write [READER_CMD_REMOVEALL main] error  [9(Bad file descriptor)] sendCmd
[01-01 08:22:51.264] (     8) [    13]     523     523  setSubLog onOff:0 logType:lastlog
[01-01 08:22:51.264] (     9) [    13]     523     523  setSubLog onOff:0 logType:uboot
[01-01 08:22:51.264] (    10) [    13]     523     523  setSubLog onOff:0 logType:android
[01-01 08:22:51.264] (    11) [    13]     523     523  setSubLog onOff:0 logType:kernel
[01-01 08:22:51.264] (    12) [    13]     523     523  setSubLog onOff:0 logType:trace
[01-01 08:22:51.264] (    13) [    13]     523     523  setSubLog onOff:0 logType:sgm
[01-01 08:22:51.264] (    14) [    13]     523     523  setSubLog onOff:0 logType:sysinfo
[01-01 08:22:51.264] (    15) [    13]     523     523  setSubLog onOff:0 logType:thermal
[01-01 08:22:51.264] (    16) [    13]     523     523  setSubLog onOff:0 logType:ylogdebug
[01-01 08:22:51.264] (    17) [    13]     523     523  setSubLog onOff:0 logType:phoneinfo
[01-01 08:22:51.264] (    18) [    13]     523     523  setSubLog onOff:1 logType:hcidump
[01-01 08:22:51.264] (    19) [    13]     523     523  [tcpdump] set bin to [tcpdump -i any -p   -U -w - -s 0 -s 3000]
[01-01 08:22:51.264] (    20) [    13]     523     523  setSubLog onOff:1 logType:tcpdump
[01-01 08:22:51.264] (    21) [    13]     523     523  setSubLog onOff:0 logType:trustlog
[01-01 08:22:51.267] (    22) [    13]     523     523  syncLegcyConfig
[01-01 08:22:51.267] (    23) [    13]     523     523  SubLog OnOff:0, logType lastlog is 1
[01-01 08:22:51.267] (    24) [    13]     523     523  SubLog OnOff:0, logType uboot is 1
[01-01 08:22:51.267] (    25) [    13]     523     523  SubLog OnOff:0, logType android is 1
[01-01 08:22:51.267] (    26) [    13]     523     523  SubLog OnOff:0, logType kernel is 1
[01-01 08:22:51.267] (    27) [    13]     523     523  SubLog OnOff:0, logType trace is 1
[01-01 08:22:51.267] (    28) [    13]     523     523  SubLog OnOff:0, logType sgm is 1
[01-01 08:22:51.268] (    29) [    13]     523     523  SubLog OnOff:0, logType sysinfo is 1
[01-01 08:22:51.268] (    30) [    13]     523     523  SubLog OnOff:0, logType thermal is 0
[01-01 08:22:51.268] (    31) [    13]     523     523  SubLog OnOff:0, logType ylogdebug is 1
[01-01 08:22:51.268] (    32) [    13]     523     523  SubLog OnOff:0, logType phoneinfo is 1
[01-01 08:22:51.268] (    33) [    13]     523     523  SubLog OnOff:1, logType hcidump is 1
[01-01 08:22:51.268] (    34) [    13]     523     523  SubLog OnOff:1, logType tcpdump is 1
[01-01 08:22:51.268] (    35) [    13]     523     523  SubLog OnOff:0, logType trustlog is 1
[01-01 08:22:51.268] (    36) [    13]     523     523  index:11,logType:tcpdump, logSize:256, totalSize:4096
[01-01 08:22:51.269] (    37) [    13]     523     523  index:10,logType:hcidump, logSize:64, totalSize:1024
[01-01 08:22:51.269] (    38) [    13]     523     523  value:default
[01-01 08:22:51.269] (    39) [    13]     523     523  make dir:/data/ylog/ap/
[01-01 08:22:51.269] (    40) [    13]     523     523  mSubLog:1
[01-01 08:22:51.269] (    41) [    13]     523     523  mSubLog:1
[01-01 08:22:51.270] (    42) [    13]     523     523  logSourceCnt:13 compressLevel:3
[01-01 08:22:51.271] (    43) [    13]     523     535  [lastlog]  configure is [1]
[01-01 08:22:51.277] (    44) [    13]     523     535  [uboot]  configure is [1]
[01-01 08:22:51.281] (    45) [    13]     523     535  [android]  configure is [1]
[01-01 08:22:51.284] (    46) [    13]     523     535  [kernel]  configure is [1]
[01-01 08:22:51.294] (    47) [    13]     523     535  [trace]  configure is [1]
[01-01 08:22:51.304] (     1) [    13]     531     531  aplogfilesize : 256
[01-01 08:22:51.306] (    48) [    13]     523     535  [sgm]  configure is [1]
[01-01 08:22:51.311] (     2) [    13]     531     531  srootdir : default/
[01-01 08:22:51.311] (     3) [    13]     531     531  aplogmaxsize : 99%
[01-01 08:22:51.312] (     4) [    13]     531     531  aplogrotate : 1
[01-01 08:22:51.312] (     5) [    13]     531     531  prioritypath : 0
[01-01 08:22:51.326] (    49) [    13]     523     535  [sysinfo]  configure is [1]
[01-01 08:22:51.335] (     6) [    13]     531     549  LogReboot:startrebootServcie
[01-01 08:22:51.335] (    50) [    13]     523     535  [thermal]  configure is [0]
[01-01 08:22:51.335] (    51) [    13]     523     535  [ylogdebug]  configure is [1]
[01-01 08:22:51.338] (     7) [    13]     531     549  mkdir /data/log/reliability/dumplog/ success
[01-01 08:22:51.361] (    52) [    13]     523     535  [phoneinfo]  configure is [1]
[01-01 08:22:51.399] (    53) [    14]     523     535  [hcidump]  configure is [1]
[01-01 08:22:51.421] (    54) [    14]     523     535  [tcpdump]  configure is [1]
[01-01 08:22:51.441] (    55) [    14]     523     535  [trustlog]  configure is [1]
[01-01 08:22:51.446] (    56) [    14]     523     535  listen to 12 source 
[01-01 08:22:51.534] (    57) [    14]     523     535  index:11 log buffer is null 
[01-01 08:22:51.535] (    58) [    14]     523     535  index:12 log buffer is null 
[01-01 08:22:52.003] (     8) [    14]     531     531  mount changed: [] -> [/data/]
[01-01 08:22:52.004] (     9) [    14]     531     531  set logdir to /data/ylog/ap/, diskfree:46278
[01-01 08:22:52.006] (    10) [    14]     531     531  last ylog file  [] not exsit,backup mmap file
[01-01 08:22:52.006] (    11) [    14]     531     531  last ylog not exit, backupMmapData error.
[01-01 08:22:52.009] (    12) [    14]     531     531  create first file
[01-01 08:22:52.011] (    13) [    14]     531     531  get new  file name(new logfile) : /data/ylog/ap/000-0101_082252_poweron.ylog 
[01-01 08:22:52.011] (    14) [    14]     531     531  open log file:/data/ylog/ap/000-0101_082252_poweron.ylog fd:23 diskfree:46278
[01-01 08:22:52.026] (    15) [    14]     531     531  update UID file:/data/ylog/loguid=0
[01-01 08:22:52.026] (    59) [    14]     523     528  get notifyNewFileHeader  but mBinHadStarted=1 ,so ignore
[01-01 08:22:52.867] (    16) [    15]     531     620  copy /blackbox/ file: /blackbox/ylog/ copied to /data/ylog/ap/blackboxlog
[01-01 08:22:52.935] (    17) [    15]     531     620  copy /cache/ file: /cache/ylog/ copied to /data/ylog/ap/cachelog
[01-01 08:22:53.556] (    60) [    16]     523     534  make dir /data/ylog/ap/tcpdump/
[01-01 08:22:54.155] (    61) [    16]     523     534  open new log:/data/ylog/ap/tcpdump/001_0101_082254_tcpdump.cap, wfd:34, logname:/data/ylog/ap/tcpdump/001_0101_082254_tcpdump.cap
[01-01 08:22:54.155] (    62) [    16]     523     534  logType->totalwriten:0 sublogsize:0
[12-31 21:22:56.339] (    18) [    18]     531     549  currentTime: 19691231212256-97219994
[12-31 21:22:56.341] (    19) [    18]     531     549  SystemBootMode::LINUXKERNEL
[12-31 21:22:56.342] (    20) [    18]     531     549  boot_cause: Pbint triggered
[12-31 21:22:56.342] (    21) [    18]     531     549  boot_reason: normalboot
[12-31 21:22:56.342] (    22) [    18]     531     549  boot_category: normalboot
[12-31 21:22:56.346] (    23) [    18]     531     549  Crash_reason: Normal
[12-31 21:22:56.356] (    24) [    18]     531     938  open /dev/block/by-name/sd_klog failed No such file or directory
[01-01 00:00:05.326] (    63) [   101]     523     534  make dir /data/ylog/ap/hcidump/
[01-01 00:00:05.350] (    64) [   101]     523     534  open new log:/data/ylog/ap/hcidump/001_0101_000005_hcidump.cap, wfd:35, logname:/data/ylog/ap/hcidump/001_0101_000005_hcidump.cap
[01-01 00:00:05.350] (    65) [   101]     523     534  logType->totalwriten:0 sublogsize:0
[01-01 11:58:37.719] (    66) [  3614]     523     532  
 2792, 2369, 1633, 1696, 1840, 1835, 1272, 1445, 1193, 1214, 1411, 1101, 1360, 1163, 1406, 1123, 1438, 1125, 1440, 1123, 1423, 1100, 1122, 1373, 1128, 1356, 1073, 1381, 1107, 1386, 1078, 1370, 1062, 1281, 1179, 1079, 1351, 1142, 1369, 1099, 1364, 1090, 1337, 1107, 1403, 1069, 1065, 1363, 1108, 1384, 1054, 1375, 1079, 1349, 1145, 1362, 1122, 1356, 1101,    0,
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
[01-01 12:59:37.798] (    67) [  7274]     523     532  
 1322, 1106, 1326, 1079, 1406, 1090, 1357, 1086, 1412, 1109, 1111, 1331, 1093, 1360, 1144, 1354, 1077, 1363, 1085, 1373, 1100, 1354, 1078, 1079, 1401, 1061, 1345, 1086, 1353, 1139, 1369, 1075, 1353, 1084, 1389, 1087, 1078, 1372, 1097, 1374, 1111, 1363, 1073, 1340, 1139, 1339, 1077, 1145, 1300, 1109, 1359, 1096, 1332, 1106, 1382, 1073, 1343, 1109, 1366, 1122,
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
[01-01 14:00:37.872] (    68) [ 10934]     523     532  
 1109, 1364, 1082, 1354, 1109, 1372, 1101, 1350, 1126, 1378, 1091, 1332, 1102, 1100, 1417, 1075, 1344, 1075, 1326, 1124, 1349, 1069, 1327, 1107, 1302, 1163, 1094, 1366, 1082, 1370, 1111, 1384, 1093, 1375, 1127, 1389, 1097, 1074, 1370, 1105, 1339, 1069, 1346, 1106, 1389, 1058, 1344, 1064, 1360, 1080, 1107, 1382, 1092, 1396, 1088, 1401, 1097, 1338, 1091, 1382,
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
[01-01 15:01:37.948] (    69) [ 14594]     523     532  
 1374, 1078, 1152, 1128, 1367, 1124, 1350, 1092, 1351, 1095, 1363, 1089, 1352, 1110, 1108, 1408, 1097, 1342, 1051, 1409, 1121, 1331, 1108, 1397, 1082, 1370, 1072, 1087, 1369, 1121, 1381, 1063, 1373, 1086, 1347, 1126, 1359, 1087, 1305, 1155, 1097, 1331, 1088, 1335, 1096, 1407, 1073, 1344, 1074, 1358, 1098, 1113, 1347, 1083, 1349, 1144, 1337, 1075, 1362, 1103,
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
[01-01 16:02:38.026] (    70) [ 18254]     523     532  
 1130, 1404, 1092, 1083, 1071, 1392, 1100, 1329, 1045, 1414, 1106, 1344, 1081, 1366, 1084, 1364, 1108, 1091, 1364, 1089, 1380, 1073, 1373, 1089, 1373, 1116, 1321, 1108, 1150, 1315, 1121, 1369, 1092, 1336, 1094, 1441, 1070, 1352, 1097, 1351, 1108, 1093, 1321, 1077, 1333, 1106, 1326, 1078, 1375, 1087, 1377, 1085, 1330, 1092, 1081, 1402, 1075, 1326, 1143, 1375,
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
[01-01 17:03:38.105] (    71) [ 21914]     523     532  
 1372, 1090, 1380, 1081, 1089, 1141, 1285, 1076, 1384, 1070, 1390, 1114, 1369, 1081, 1357, 1123, 1374, 1083, 1096, 1356, 1119, 1364, 1059, 1342, 1101, 1380, 1072, 1365, 1090, 1369, 1108, 1111, 1341, 1073, 1368, 1092, 1317, 1101, 1371, 1101, 1396, 1106, 1227, 1185, 1097, 1385, 1090, 1362, 1084, 1357, 1080, 1372, 1083, 1359, 1096, 1067, 1372, 1109, 1366, 1074,
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
[01-01 18:04:38.185] (    72) [ 25574]     523     532  
 1111, 1372, 1072, 1386, 1088, 1086, 1385, 1096, 1091, 1342, 1096, 1349, 1085, 1353, 1098, 1350, 1122, 1364, 1078, 1202, 1237, 1118, 1354, 1088, 1388, 1084, 1410, 1126, 1350, 1092, 1386, 1101, 1088, 1385, 1079, 1371, 1113, 1353, 1094, 1321, 1114, 1390, 1085, 1318, 1121, 1095, 1383, 1087, 1344, 1090, 1377, 1104, 1327, 1107, 1335, 1080, 1132, 1315, 1101, 1350,
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
[01-01 19:05:38.262] (    73) [ 29234]     523     532  
 1358, 1107, 1316, 1086, 1339, 1091, 1135, 1340, 1064, 1087, 1384, 1144, 1358, 1090, 1355, 1101, 1387, 1087, 1348, 1073, 1359, 1145, 1085, 1335, 1083, 1365, 1133, 1349, 1091, 1372, 1102, 1409, 1070, 1124, 1334, 1090, 1409, 1084, 1329, 1088, 1354, 1116, 1335, 1112, 1354, 1078, 1120, 1362, 1076, 1357, 1097, 1401, 1087, 1383, 1096, 1377, 1123, 1337, 1106, 1112,
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
[01-01 20:06:38.338] (    74) [ 32894]     523     532  
 1081, 1322, 1107, 1362, 1070, 1356, 1085, 1096, 1338, 1118, 1144, 1331, 1082, 1342, 1066, 1336, 1121, 1334, 1099, 1353, 1080, 1375, 1117, 1055, 1335, 1100, 1395, 1094, 1364, 1080, 1334, 1108, 1387, 1069, 1339, 1079, 1113, 1357, 1076, 1340, 1075, 1388, 1115, 1323, 1077, 1343, 1119, 1286, 1168, 1099, 1366, 1054, 1377, 1082, 1344, 1075, 1352, 1122, 1381, 1111,
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
[01-01 21:07:38.420] (    75) [ 36554]     523     532  
 1093, 1279, 1199, 1089, 1365, 1114, 1347, 1101, 1063, 1392, 1082, 1332, 1123, 1094, 1388, 1096, 1378, 1101, 1355, 1107, 1330, 1072, 1436, 1073, 1271, 1143, 1100, 1415, 1070, 1388, 1066, 1348, 1132, 1354, 1088, 1350, 1096, 1125, 1370, 1128, 1353, 1059, 1405, 1108, 1354, 1074, 1378, 1134, 1369, 1088, 1078, 1341, 1111, 1357, 1076, 1360, 1095, 1355, 1065, 1371,
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
[01-01 22:08:38.497] (    76) [ 40214]     523     532  
 1360, 1106, 1384, 1066, 1115, 1348, 1088, 1376, 1066, 1099, 1354, 1081, 1405, 1070, 1099, 1356, 1076, 1371, 1087, 1358, 1086, 1363, 1133, 1352, 1066, 1362, 1089, 1118, 1383, 1094, 1350, 1075, 1385, 1081, 1330, 1113, 1364, 1115, 1301, 1136, 1057, 1385, 1132, 1342, 1097, 1364, 1090, 1360, 1097, 1352, 1081, 1129, 1349, 1122, 1362, 1094, 1351, 1113, 1356, 1068,
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
[01-01 23:09:38.574] (    77) [ 43874]     523     532  
 1102, 1360, 1095, 1408, 1079, 1075, 1379, 1062, 1387, 1078, 1090, 1372, 1140, 1362, 1089, 1279, 1135, 1149, 1348, 1079, 1347, 1103, 1384, 1098, 1358, 1093, 1342, 1129, 1082, 1327, 1086, 1365, 1102, 1337, 1115, 1352, 1070, 1391, 1097, 1352, 1103, 1079, 1381, 1102, 1348, 1105, 1350, 1102, 1368, 1075, 1369, 1058, 1282, 1174, 1080, 1354, 1092, 1386, 1120, 1360,
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
[01-02 00:10:38.644] (    78) [ 47534]     523     532  
 3441, 3209, 3470, 3217, 3489, 1686, 1339, 1180, 1122, 1385, 1144, 1083, 1364, 1150, 1378, 1091, 1375, 1070, 1111, 1367, 1110, 1378, 1080, 1426, 1073, 1387, 1107, 1346, 1128, 1100, 1360, 1074, 1384, 1142, 1338, 1088, 1396, 1078, 1389, 1100, 1379, 1084, 1078, 1417, 1089, 1345, 1102, 1369, 1107, 1368, 1072, 1355, 1088, 1378, 1096, 1077, 1515, 1927, 3669, 3248,
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
[01-02 01:10:38.723] (    79) [ 51135]     523     532  
 1074, 1364, 1109, 1390, 1075, 1343, 1080, 1292, 1202, 1077, 1345, 1352, 1084, 1390, 1114, 1324, 1100, 1346, 1137, 1063, 1358, 1100, 1342, 1092, 1383, 1097, 1370, 1114, 1403, 1078, 1320, 1110, 1069, 1406, 1113, 1358, 1105, 1396, 1128, 1375, 1107, 1399, 1087, 1129, 1382, 1091, 1365, 1104, 1406, 1057, 1382, 1077, 1385, 1114, 1391, 1080, 1069, 1544, 1185, 1324,
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
[01-02 02:11:38.805] (    80) [ 54795]     523     532  
 1387, 1083, 1358, 1076, 1359, 1078, 1361, 1075, 1286, 1166, 1088, 1359, 1360, 1144, 1392, 1080, 1332, 1099, 1384, 1061, 1083, 1357, 1058, 1366, 1088, 1393, 1095, 1344, 1125, 1341, 1089, 1325, 1087, 1123, 1372, 1070, 1360, 1099, 1375, 1080, 1376, 1092, 1342, 1091, 1162, 1343, 1071, 1377, 1074, 1375, 1100, 1333, 1058, 1363, 1122, 1387, 1071, 1085, 1353, 1114,
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
[01-02 03:12:38.886] (    81) [ 58455]     523     532  
 1091, 1345, 1080, 1396, 1099, 1347, 1093, 1370, 1081, 1321, 1132, 1074, 1350, 1371, 1117, 1378, 1102, 1357, 1084, 1412, 1079, 1060, 1365, 1083, 1368, 1069, 1405, 1075, 1339, 1121, 1412, 1076, 1329, 1121, 1101, 1344, 1102, 1341, 1080, 1413, 1098, 1352, 1071, 1371, 1133, 1085, 1334, 1073, 1329, 1125, 1330, 1078, 1382, 1096, 1370, 1073, 1374, 1080, 1092, 1387,
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
[01-02 04:13:38.956] (    82) [ 62115]     523     532  
 1354, 1083, 1390, 1070, 1398, 1096, 1360, 1081, 1382, 1101, 1273, 1148, 1098, 1356, 1402, 1090, 1371, 1061, 1343, 1131, 1354, 1081, 1090, 1361, 1109, 1366, 1075, 1361, 1083, 1420, 1075, 1373, 1112, 1343, 1112, 1088, 1391, 1081, 1342, 1137, 1333, 1073, 1379, 1084, 1383, 1100, 1117, 1340, 1106, 1402, 1089, 1354, 1117, 1376, 1120, 1356, 1070, 1370, 1087, 1121,
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
[01-02 05:14:39.029] (    83) [ 65775]     523     532  
 1098, 1371, 1083, 1328, 1092, 1373, 1058, 1363, 1097, 1354, 1135, 1275, 1149, 1083, 1383, 1374, 1073, 1337, 1114, 1344, 1084, 1400, 1081, 1058, 1390, 1083, 1374, 1076, 1354, 1088, 1366, 1099, 1369, 1090, 1361, 1127, 1063, 1384, 1110, 1333, 1103, 1367, 1055, 1385, 1072, 1381, 1069, 1195, 1338, 1084, 1411, 1101, 1363, 1076, 1407, 1104, 1447, 1185, 1377, 1070,
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
[07-25 17:04:53.861] (    84) [ 69338]     523     539  rcv clear cmd
[07-25 17:04:53.861] (    85) [ 69338]     523     539  mYlogClear:1
[07-25 17:04:53.861] (    86) [ 69338]     523     534  sublog recv clear cmd
[07-25 17:04:53.863] (    25) [ 69338]     531     531  logfilewriter rcv clear cmd clear
[07-25 17:04:53.886] (    26) [ 69338]     531     531  log file closed(clear): /data/ylog/ap/000-0101_082252_poweron.ylog fd:23 size:216464591
[07-25 17:04:53.892] (    27) [ 69338]     531     531  clear rmdir:/cache/ylog/ap/history/
[07-25 17:04:53.892] (    28) [ 69338]     531     531  clear rmdir:/data/ylog/ap/history/
[07-25 17:04:53.893] (    29) [ 69338]     531     531  clear rmdir:/storage/sdcard0/ylog/ap/history/
[07-25 17:04:53.893] (    30) [ 69338]     531     531  clear rmdir:/storage/emulated/0/ylog/ap/history/
[07-25 17:04:53.894] (    31) [ 69338]     531     531  clear rmdir:/data/ylog/ap/history/
[07-25 17:04:53.894] (    32) [ 69338]     531     531  clear rmdir:/cache/ylog/last_ylog/
[07-25 17:04:53.897] (    33) [ 69338]     531     531  clear rmdir:/data/ylog/last_ylog/
[07-25 17:04:53.897] (    34) [ 69338]     531     531  clear rmdir:/storage/sdcard0/ylog/last_ylog/
[07-25 17:04:53.897] (    35) [ 69338]     531     531  clear rmdir:/storage/emulated/0/ylog/last_ylog/
[07-25 17:04:53.897] (    87) [ 69338]     523     534  reset persist.sys.ylog.hcidump
[07-25 17:04:53.898] (    36) [ 69338]     531     531  clear rmdir:/data/ylog/last_ylog/
[07-25 17:04:53.898] (    37) [ 69338]     531     531  clear rmdir:/cache/ylog/SYSDUMP/
[07-25 17:04:53.898] (    38) [ 69338]     531     531  clear rmdir:/data/ylog/SYSDUMP/
[07-25 17:04:53.898] (    39) [ 69338]     531     531  clear rmdir:/storage/sdcard0/ylog/SYSDUMP/
[07-25 17:04:53.898] (    40) [ 69338]     531     531  clear rmdir:/storage/emulated/0/ylog/SYSDUMP/
[07-25 17:04:53.899] (    41) [ 69338]     531     531  clear rmdir:/data/ylog/SYSDUMP/
[07-25 17:04:53.899] (    42) [ 69338]     531     531  clear rmdir:/cache/ylog/ap/current/
[07-25 17:04:53.899] (    43) [ 69338]     531     531  clear rmdir:/data/ylog/ap/current/
[07-25 17:04:53.899] (    44) [ 69338]     531     531  clear rmdir:/storage/sdcard0/ylog/ap/current/
[07-25 17:04:53.900] (    45) [ 69338]     531     531  clear rmdir:/storage/emulated/0/ylog/ap/current/
[07-25 17:04:53.900] (    46) [ 69338]     531     531  clear rmdir:/data/ylog/ap/current/
[07-25 17:04:53.900] (    47) [ 69338]     531     531  clear rmdir:/cache//ylog/poweron/ap/
[07-25 17:04:53.900] (    48) [ 69338]     531     531  clear rmdir:/data//ylog/poweron/ap/
[07-25 17:04:53.901] (    49) [ 69338]     531     531  clear rmdir:/storage/sdcard0//ylog/poweron/ap/
[07-25 17:04:53.901] (    50) [ 69338]     531     531  clear rmdir:/storage/emulated/0//ylog/poweron/ap/
[07-25 17:04:53.902] (    51) [ 69338]     531     531  clear rmdir:/data//ylog/poweron/ap/
[07-25 17:04:53.902] (    52) [ 69338]     531     531  clear rmdir:/cache//slog/
[07-25 17:04:53.904] (    53) [ 69338]     531     531  clear rmdir:/data//slog/
[07-25 17:04:53.904] (    54) [ 69338]     531     531  clear rmdir:/storage/sdcard0//slog/
[07-25 17:04:53.904] (    55) [ 69338]     531     531  clear rmdir:/storage/emulated/0//slog/
[07-25 17:04:53.905] (    56) [ 69338]     531     531  clear rmdir:/data//slog/
[07-25 17:04:53.905] (    57) [ 69338]     531     531  clear rmdir:/cache//ylog/ap/SYSDUMP/
[07-25 17:04:53.905] (    58) [ 69338]     531     531  clear rmdir:/data//ylog/ap/SYSDUMP/
[07-25 17:04:53.905] (    59) [ 69338]     531     531  clear rmdir:/storage/sdcard0//ylog/ap/SYSDUMP/
[07-25 17:04:53.905] (    60) [ 69338]     531     531  clear rmdir:/storage/emulated/0//ylog/ap/SYSDUMP/
[07-25 17:04:53.906] (    61) [ 69338]     531     531  clear rmdir:/data//ylog/ap/SYSDUMP/
[07-25 17:04:53.906] (    62) [ 69338]     531     531  clear rmdir:/cache/ylog/ap/
[07-25 17:04:53.906] (    63) [ 69338]     531     531  clear rmdir:/data/ylog/ap/
[07-25 17:04:53.910] (    88) [ 69338]     523     534  reset persist.sys.ylog.tcpdump
[07-25 17:04:53.910] (    89) [ 69338]     523     534  notifyOpenBin: all
[07-25 17:04:54.219] (    90) [ 69338]     523     538  reqNewLogBuff mNewFileType:all
[07-25 17:04:54.404] (    64) [ 69338]     531     531  clear rmdir:/storage/sdcard0/ylog/ap/
[07-25 17:04:54.405] (    65) [ 69338]     531     531  clear rmdir:/storage/emulated/0/ylog/ap/
[07-25 17:04:54.406] (    66) [ 69338]     531     531  clear rmdir:/data/ylog/ap/
[07-25 17:04:54.406] (    67) [ 69338]     531     531  clear rmfile:/cache/ylog/ylogtrace.info
[07-25 17:04:54.406] (    68) [ 69338]     531     531  clear rmfile:/cache/ylog/fwcrash.info
[07-25 17:04:54.406] (    69) [ 69338]     531     531  clear rmfile:/data/ylog/ylogtrace.info
[07-25 17:04:54.407] (    70) [ 69338]     531     531  clear rmfile:/data/ylog/fwcrash.info
[07-25 17:04:54.407] (    71) [ 69338]     531     531  clear rmfile:/storage/sdcard0/ylog/ylogtrace.info
[07-25 17:04:54.407] (    72) [ 69338]     531     531  clear rmfile:/storage/sdcard0/ylog/fwcrash.info
[07-25 17:04:54.407] (    73) [ 69338]     531     531  clear rmfile:/storage/emulated/0/ylog/ylogtrace.info
[07-25 17:04:54.408] (    74) [ 69338]     531     531  clear rmfile:/storage/emulated/0/ylog/fwcrash.info
[07-25 17:04:54.409] (    75) [ 69338]     531     531  clear rmfile:/data/ylog/ylogtrace.info
[07-25 17:04:54.409] (    76) [ 69338]     531     531  clear rmfile:/data/ylog/fwcrash.info
[07-25 17:04:54.415] (    77) [ 69338]     531     531  uniqueID file deleted(/data/ylog/loguid) ret=0 
[07-25 17:04:54.415] (    78) [ 69338]     531     531  clear log take 0s
[07-25 17:04:54.911] (    91) [ 69339]     523     534  wait for drop log, cnt:29
[07-25 17:04:55.911] (    92) [ 69340]     523     534  wait for drop log, cnt:28
[07-25 17:04:55.966] (    79) [ 69340]     531     621  __ERROR  write to fd -1 ret -1  error  [9(Bad file descriptor)] writeToFD
[07-25 17:04:55.980] (    80) [ 69340]     531     621  __ERROR  write to fd -1 ret -1  error  [9(Bad file descriptor)] writeToFD
[07-25 17:04:55.991] (    81) [ 69340]     531     621  __ERROR  write to fd -1 ret -1  error  [9(Bad file descriptor)] writeToFD
[07-25 17:04:56.001] (    82) [ 69340]     531     621  __ERROR  write to fd -1 ret -1  error  [9(Bad file descriptor)] writeToFD
[07-25 17:04:56.012] (    83) [ 69340]     531     621  __ERROR  write to fd -1 ret -1  error  [9(Bad file descriptor)] writeToFD
[07-25 17:04:56.022] (    84) [ 69340]     531     621  __ERROR  retries all write to fd -1   error  [9(Bad file descriptor)] writeToFD
[07-25 17:04:56.022] (    85) [ 69340]     531     621  __ERROR  flush buffer error error  [9(Bad file descriptor)] flushIOBuff
[07-25 17:04:56.912] (    93) [ 69341]     523     534  wait for drop log, cnt:27
[07-25 17:04:57.323] (    94) [ 69341]     523     538  setDropLog:1
[07-25 17:04:57.445] (    95) [ 69341]     523     535  [uboot] set [0]
[07-25 17:04:57.568] (    96) [ 69341]     523     535  [lastlog] set [0]
[07-25 17:04:57.674] (    97) [ 69341]     523     535  [kernel] set [0]
[07-25 17:04:57.692] (    98) [ 69341]     523     535  [android] set [0]
[07-25 17:04:57.705] (    99) [ 69341]     523     535  __ERROR  [audiodump]close source  failed<cmd> error  [4(Interrupted system call)] UnListenLogSource
[07-25 17:04:57.717] (   100) [ 69342]     523     539  rcv disable cmd , cur enable=1
[07-25 17:04:57.717] (   101) [ 69342]     523     539  [status] set [disable]
[07-25 17:04:57.719] (   102) [ 69342]     523     539  cmd set  LogEnable=0
[07-25 17:04:57.852] (   103) [ 69342]     523     535  sync Log to file
[07-25 17:04:57.852] (   104) [ 69342]     523     535  mSyncLog:1
[07-25 17:04:57.867] (   105) [ 69342]     523     539  gYlogStatus:0
[07-25 17:04:57.912] (   106) [ 69342]     523     534  wait for drop log, cnt:26
[07-25 17:04:57.919] (   107) [ 69342]     523     534  setDropLog:0
[07-25 17:04:57.919] (   108) [ 69342]     523     534  ylogptr-name:11
[07-25 17:04:57.919] (   109) [ 69342]     523     534  make dir /data/ylog/ap/hcidump/
[07-25 17:04:57.939] (   110) [ 69342]     523     534  open new log:/data/ylog/ap/hcidump/001_0725_170457_hcidump.cap, wfd:23, logname:/data/ylog/ap/hcidump/001_0725_170457_hcidump.cap
[07-25 17:04:57.939] (   111) [ 69342]     523     534  logType->totalwriten:0 sublogsize:0
[07-25 17:04:57.939] (   112) [ 69342]     523     534  ylogptr-name:12
[07-25 17:04:57.939] (   113) [ 69342]     523     534  make dir /data/ylog/ap/tcpdump/
[07-25 17:04:57.976] (   114) [ 69342]     523     534  open new log:/data/ylog/ap/tcpdump/001_0725_170457_tcpdump.cap, wfd:24, logname:/data/ylog/ap/tcpdump/001_0725_170457_tcpdump.cap
[07-25 17:04:57.977] (   115) [ 69342]     523     534  logType->totalwriten:0 sublogsize:0
[07-25 17:04:57.977] (   116) [ 69342]     523     534  clear sublog end
[07-25 17:04:58.324] (   117) [ 69342]     523     538  wait 1s to drop log:0, cnt：29
[07-25 17:04:58.365] (   118) [ 69342]     523     538   LogSerilizer  stoped
[07-25 17:04:58.366] (   119) [ 69342]     523     538  clear buff[-1]  all logs  9 rec  by serialize stoped[15E2D4-15E2D4]
[07-25 17:04:58.466] (   120) [ 69342]     523     538   LogSerilizer  stoped
[07-25 17:04:58.467] (   121) [ 69342]     523     538  clear buff[-1]  all logs  48 rec  by serialize stoped[15E304-15E304]
[07-25 17:04:58.476] (   122) [ 69342]     523     535  index:11 log buffer is null 
[07-25 17:04:58.480] (   123) [ 69342]     523     535  index:12 log buffer is null 
[07-25 17:04:58.483] (   124) [ 69342]     523     534  sublog recv disable cmd
[07-25 17:04:58.483] (   125) [ 69342]     523     534  retryCnt:10, mSyncLog:1
[07-25 17:04:58.484] (   126) [ 69342]     523     534  no data to sync
[07-25 17:04:58.487] (   127) [ 69342]     523     534  no data to sync
[07-25 17:04:58.488] (   128) [ 69342]     523     534  sync 24 log to file
[07-25 17:04:58.488] (   129) [ 69342]     523     534  /data/ylog/ap/hcidump/001_0725_170457_hcidump.cap rename /data/ylog/ap/hcidump/001_0725_170457_0725_170458_hcidump.cap tmp:/data/ylog/ap/hcidump/001_0725_170457_
[07-25 17:04:58.488] (   130) [ 69342]     523     534  /data/ylog/ap/tcpdump/001_0725_170457_tcpdump.cap rename /data/ylog/ap/tcpdump/001_0725_170457_0725_170458_tcpdump.cap tmp:/data/ylog/ap/tcpdump/001_0725_170457_
[07-25 17:04:59.485] (   131) [ 69343]     523     535  index:11 log buffer is null 
[07-25 17:04:59.489] (   132) [ 69343]     523     535  index:12 log buffer is null 
[07-25 17:04:59.768] (   133) [ 69344]     523     538   LogSerilizer  stoped
[07-25 17:04:59.768] (   134) [ 69344]     523     538  clear buff[-1]  all logs  18 rec  by serialize stoped[15E316-15E316]
[07-25 17:05:00.470] (   135) [ 69344]     523     538   LogSerilizer  stoped
[07-25 17:05:00.470] (   136) [ 69344]     523     538  clear buff[-1]  all logs  17 rec  by serialize stoped[15E327-15E327]
[07-25 17:05:01.101] (   137) [ 69345]     523     539  rcv clear cmd
[07-25 17:05:01.101] (   138) [ 69345]     523     539  mYlogClear:1
[07-25 17:05:01.101] (    86) [ 69345]     531     531  logfilewriter rcv clear cmd clear
[07-25 17:05:01.103] (    87) [ 69345]     531     531  clear rmdir:/cache/ylog/ap/history/
[07-25 17:05:01.104] (    88) [ 69345]     531     531  clear rmdir:/data/ylog/ap/history/
[07-25 17:05:01.104] (    89) [ 69345]     531     531  clear rmdir:/storage/sdcard0/ylog/ap/history/
[07-25 17:05:01.104] (    90) [ 69345]     531     531  clear rmdir:/storage/emulated/0/ylog/ap/history/
[07-25 17:05:01.107] (    91) [ 69345]     531     531  clear rmdir:/data/ylog/ap/history/
[07-25 17:05:01.107] (    92) [ 69345]     531     531  clear rmdir:/cache/ylog/last_ylog/
[07-25 17:05:01.107] (    93) [ 69345]     531     531  clear rmdir:/data/ylog/last_ylog/
[07-25 17:05:01.107] (    94) [ 69345]     531     531  clear rmdir:/storage/sdcard0/ylog/last_ylog/
[07-25 17:05:01.107] (    95) [ 69345]     531     531  clear rmdir:/storage/emulated/0/ylog/last_ylog/
[07-25 17:05:01.111] (    96) [ 69345]     531     531  clear rmdir:/data/ylog/last_ylog/
[07-25 17:05:01.111] (    97) [ 69345]     531     531  clear rmdir:/cache/ylog/SYSDUMP/
[07-25 17:05:01.111] (    98) [ 69345]     531     531  clear rmdir:/data/ylog/SYSDUMP/
[07-25 17:05:01.111] (    99) [ 69345]     531     531  clear rmdir:/storage/sdcard0/ylog/SYSDUMP/
[07-25 17:05:01.111] (   100) [ 69345]     531     531  clear rmdir:/storage/emulated/0/ylog/SYSDUMP/
[07-25 17:05:01.112] (   101) [ 69345]     531     531  clear rmdir:/data/ylog/SYSDUMP/
[07-25 17:05:01.113] (   102) [ 69345]     531     531  clear rmdir:/cache/ylog/ap/current/
[07-25 17:05:01.113] (   103) [ 69345]     531     531  clear rmdir:/data/ylog/ap/current/
[07-25 17:05:01.113] (   104) [ 69345]     531     531  clear rmdir:/storage/sdcard0/ylog/ap/current/
[07-25 17:05:01.113] (   105) [ 69345]     531     531  clear rmdir:/storage/emulated/0/ylog/ap/current/
[07-25 17:05:01.114] (   106) [ 69345]     531     531  clear rmdir:/data/ylog/ap/current/
[07-25 17:05:01.114] (   107) [ 69345]     531     531  clear rmdir:/cache//ylog/poweron/ap/
[07-25 17:05:01.114] (   108) [ 69345]     531     531  clear rmdir:/data//ylog/poweron/ap/
[07-25 17:05:01.114] (   109) [ 69345]     531     531  clear rmdir:/storage/sdcard0//ylog/poweron/ap/
[07-25 17:05:01.114] (   110) [ 69345]     531     531  clear rmdir:/storage/emulated/0//ylog/poweron/ap/
[07-25 17:05:01.115] (   111) [ 69345]     531     531  clear rmdir:/data//ylog/poweron/ap/
[07-25 17:05:01.115] (   112) [ 69345]     531     531  clear rmdir:/cache//slog/
[07-25 17:05:01.115] (   113) [ 69345]     531     531  clear rmdir:/data//slog/
[07-25 17:05:01.115] (   114) [ 69345]     531     531  clear rmdir:/storage/sdcard0//slog/
[07-25 17:05:01.115] (   115) [ 69345]     531     531  clear rmdir:/storage/emulated/0//slog/
[07-25 17:05:01.116] (   116) [ 69345]     531     531  clear rmdir:/data//slog/
[07-25 17:05:01.116] (   117) [ 69345]     531     531  clear rmdir:/cache//ylog/ap/SYSDUMP/
[07-25 17:05:01.116] (   118) [ 69345]     531     531  clear rmdir:/data//ylog/ap/SYSDUMP/
[07-25 17:05:01.117] (   119) [ 69345]     531     531  clear rmdir:/storage/sdcard0//ylog/ap/SYSDUMP/
[07-25 17:05:01.117] (   120) [ 69345]     531     531  clear rmdir:/storage/emulated/0//ylog/ap/SYSDUMP/
[07-25 17:05:01.118] (   121) [ 69345]     531     531  clear rmdir:/data//ylog/ap/SYSDUMP/
[07-25 17:05:01.118] (   122) [ 69345]     531     531  clear rmdir:/cache/ylog/ap/
[07-25 17:05:01.118] (   123) [ 69345]     531     531  clear rmdir:/data/ylog/ap/
[07-25 17:05:01.119] (   124) [ 69345]     531     531  clear rmdir:/storage/sdcard0/ylog/ap/
[07-25 17:05:01.119] (   125) [ 69345]     531     531  clear rmdir:/storage/emulated/0/ylog/ap/
[07-25 17:05:01.120] (   126) [ 69345]     531     531  clear rmdir:/data/ylog/ap/
[07-25 17:05:01.120] (   127) [ 69345]     531     531  clear rmfile:/cache/ylog/ylogtrace.info
[07-25 17:05:01.120] (   128) [ 69345]     531     531  clear rmfile:/cache/ylog/fwcrash.info
[07-25 17:05:01.120] (   129) [ 69345]     531     531  clear rmfile:/data/ylog/ylogtrace.info
[07-25 17:05:01.120] (   130) [ 69345]     531     531  clear rmfile:/data/ylog/fwcrash.info
[07-25 17:05:01.120] (   131) [ 69345]     531     531  clear rmfile:/storage/sdcard0/ylog/ylogtrace.info
[07-25 17:05:01.120] (   132) [ 69345]     531     531  clear rmfile:/storage/sdcard0/ylog/fwcrash.info
[07-25 17:05:01.120] (   133) [ 69345]     531     531  clear rmfile:/storage/emulated/0/ylog/ylogtrace.info
[07-25 17:05:01.123] (   134) [ 69345]     531     531  clear rmfile:/storage/emulated/0/ylog/fwcrash.info
[07-25 17:05:01.124] (   135) [ 69345]     531     531  clear rmfile:/data/ylog/ylogtrace.info
[07-25 17:05:01.125] (   136) [ 69345]     531     531  clear rmfile:/data/ylog/fwcrash.info
[07-25 17:05:01.127] (   137) [ 69345]     531     531  uniqueID file deleted(/data/ylog/loguid) ret=-1 No such file or directory
[07-25 17:05:01.127] (   138) [ 69345]     531     531  clear log take 0s
[07-25 17:05:01.772] (   139) [ 69346]     523     538   LogSerilizer  stoped
[07-25 17:05:01.772] (   140) [ 69346]     523     538  clear buff[-1]  all logs  18 rec  by serialize stoped[15E339-15E339]
[07-25 17:05:02.466] (   141) [ 69346]     523     539  rcv enable cmd , cur enable=0
[07-25 17:05:02.466] (   142) [ 69346]     523     539  [status] set [enable]
[07-25 17:05:02.467] (   143) [ 69346]     523     539  cmd set  LogEnable=1
[07-25 17:05:02.467] (   144) [ 69346]     523     539  gYlogStatus:1
[07-25 17:05:02.467] (   145) [ 69346]     523     539  startlogServcie LogEnable : 1
[07-25 17:05:02.473] (   146) [ 69346]     523     538   LogSerilizer  stoped
[07-25 17:05:02.473] (   147) [ 69346]     523     538  clear buff[-1]  all logs  16 rec  by serialize stoped[15E349-15E349]
[07-25 17:05:05.480] (   148) [ 69349]     523     539  clear sublog params:13
[07-25 17:05:05.481] (   149) [ 69349]     523     539  setSubLog onOff:0 logType:lastlog
[07-25 17:05:05.481] (   150) [ 69349]     523     539  setSubLog onOff:0 logType:uboot
[07-25 17:05:05.482] (   151) [ 69349]     523     539  setSubLog onOff:0 logType:android
[07-25 17:05:05.482] (   152) [ 69349]     523     539  setSubLog onOff:0 logType:kernel
[07-25 17:05:05.482] (   153) [ 69349]     523     539  setSubLog onOff:0 logType:trace
[07-25 17:05:05.482] (   154) [ 69349]     523     539  setSubLog onOff:0 logType:sgm
[07-25 17:05:05.482] (   155) [ 69349]     523     539  setSubLog onOff:0 logType:sysinfo
[07-25 17:05:05.482] (   156) [ 69349]     523     539  setSubLog onOff:0 logType:thermal
[07-25 17:05:05.482] (   157) [ 69349]     523     539  setSubLog onOff:0 logType:ylogdebug
[07-25 17:05:05.482] (   158) [ 69349]     523     539  setSubLog onOff:0 logType:phoneinfo
[07-25 17:05:05.482] (   159) [ 69349]     523     539  setSubLog onOff:1 logType:hcidump
[07-25 17:05:05.482] (   160) [ 69349]     523     539  [tcpdump] set bin to [tcpdump -i any -p   -U -w - -s 0 -s 3000]
[07-25 17:05:05.482] (   161) [ 69349]     523     539  setSubLog onOff:1 logType:tcpdump
[07-25 17:05:05.482] (   162) [ 69349]     523     539  setSubLog onOff:0 logType:trustlog
[07-25 17:05:05.491] (   163) [ 69349]     523     539  syncLegcyConfig
[07-25 17:05:05.492] (   164) [ 69349]     523     539  SubLog OnOff:0, logType lastlog is 0
[07-25 17:05:05.492] (   165) [ 69349]     523     539  SubLog OnOff:0, logType uboot is 0
[07-25 17:05:05.492] (   166) [ 69349]     523     539  SubLog OnOff:0, logType android is 0
[07-25 17:05:05.492] (   167) [ 69349]     523     539  SubLog OnOff:0, logType kernel is 0
[07-25 17:05:05.492] (   168) [ 69349]     523     539  SubLog OnOff:0, logType trace is 1
[07-25 17:05:05.492] (   169) [ 69349]     523     539  SubLog OnOff:0, logType sgm is 1
[07-25 17:05:05.492] (   170) [ 69349]     523     539  SubLog OnOff:0, logType sysinfo is 1
[07-25 17:05:05.492] (   171) [ 69349]     523     539  SubLog OnOff:0, logType thermal is 0
[07-25 17:05:05.492] (   172) [ 69349]     523     539  SubLog OnOff:0, logType ylogdebug is 1
[07-25 17:05:05.492] (   173) [ 69349]     523     539  SubLog OnOff:0, logType phoneinfo is 1
[07-25 17:05:05.492] (   174) [ 69349]     523     539  SubLog OnOff:1, logType hcidump is 1
[07-25 17:05:05.492] (   175) [ 69349]     523     539  SubLog OnOff:1, logType tcpdump is 1
[07-25 17:05:05.492] (   176) [ 69349]     523     539  SubLog OnOff:0, logType trustlog is 1
[07-25 17:05:05.494] (   177) [ 69349]     523     539  index:11,logType:tcpdump, logSize:256, totalSize:4096
[07-25 17:05:05.494] (   178) [ 69349]     523     539  index:10,logType:hcidump, logSize:64, totalSize:1024
[07-25 17:05:05.494] (   179) [ 69349]     523     539  value:default
[07-25 17:05:05.494] (   180) [ 69349]     523     539  make dir:/data/ylog/ap/
[07-25 17:05:05.494] (   181) [ 69349]     523     539  clearLogList remove  ptr:0xb400006f45993b30
[07-25 17:05:05.495] (   182) [ 69349]     523     539  clearLogList remove  ptr:0xb400006f45994630
[07-25 17:05:05.495] (   183) [ 69349]     523     539  mSubLog:1
[07-25 17:05:05.495] (   184) [ 69349]     523     539  mSubLog:1
[07-25 17:05:05.496] (   185) [ 69349]     523     535  [lastlog]  configure is [0]
[07-25 17:05:05.496] (   186) [ 69349]     523     535  [uboot]  configure is [0]
[07-25 17:05:05.496] (   187) [ 69349]     523     535  [android]  configure is [0]
[07-25 17:05:05.496] (   188) [ 69349]     523     535  [kernel]  configure is [0]
[07-25 17:05:05.496] (   189) [ 69349]     523     535  [trace]  configure is [1]
[07-25 17:05:05.496] (   190) [ 69349]     523   26715  make dir /data/ylog/ap/hcidump/
[07-25 17:05:05.504] (   191) [ 69349]     523     535  [sgm]  configure is [1]
[07-25 17:05:05.505] (   192) [ 69349]     523     535  [sysinfo]  configure is [1]
[07-25 17:05:05.510] (   193) [ 69349]     523     535  [thermal]  configure is [0]
[07-25 17:05:05.510] (   194) [ 69349]     523     535  [ylogdebug]  configure is [1]
[07-25 17:05:05.516] (   195) [ 69349]     523   26715  open new log:/data/ylog/ap/hcidump/002_0725_170505_hcidump.cap, wfd:25, logname:/data/ylog/ap/hcidump/002_0725_170505_hcidump.cap
[07-25 17:05:05.517] (   196) [ 69349]     523   26715  logType->totalwriten:0 sublogsize:0
[07-25 17:05:05.536] (   197) [ 69349]     523     535  [phoneinfo]  configure is [1]
[07-25 17:05:05.566] (   198) [ 69349]     523     535  [hcidump]  configure is [1]
[07-25 17:05:05.590] (   199) [ 69349]     523     535  [tcpdump]  configure is [1]
[07-25 17:05:05.634] (   200) [ 69349]     523     535  [trustlog]  configure is [1]
[07-25 17:05:05.688] (   201) [ 69349]     523     535  listen to 8 source 
[07-25 17:05:05.726] (   202) [ 69350]     523     535  ListenLogSource setValue:uboot
[07-25 17:05:05.726] (   203) [ 69350]     523     535  [uboot] set [1]
[07-25 17:05:05.760] (   204) [ 69350]     523     535  ListenLogSource setValue:lastlog
[07-25 17:05:05.760] (   205) [ 69350]     523     535  [lastlog] set [1]
[07-25 17:05:05.816] (   206) [ 69350]     523     535  ListenLogSource setValue:kernel
[07-25 17:05:05.816] (   207) [ 69350]     523     535  [kernel] set [1]
[07-25 17:05:05.821] (   208) [ 69350]     523     535  index:11 log buffer is null 
[07-25 17:05:05.826] (   209) [ 69350]     523     535  index:12 log buffer is null 
[07-25 17:05:05.837] (   210) [ 69350]     523   26715  make dir /data/ylog/ap/tcpdump/
[07-25 17:05:05.848] (   211) [ 69350]     523     535  ListenLogSource setValue:android
[07-25 17:05:05.848] (   212) [ 69350]     523     535  [android] set [1]
[07-25 17:05:05.859] (   213) [ 69350]     523   26715  open new log:/data/ylog/ap/tcpdump/002_0725_170505_tcpdump.cap, wfd:37, logname:/data/ylog/ap/tcpdump/002_0725_170505_tcpdump.cap
[07-25 17:05:05.860] (   214) [ 69350]     523   26715  logType->totalwriten:0 sublogsize:0
[07-25 17:05:05.889] (   215) [ 69350]     523     535  __ERROR  [audiodump]close source  failed<cmd> error  [2(No such file or directory)] UnListenLogSource
[07-25 17:05:05.893] (   216) [ 69350]     523     539  [tcpdump]bin file changed2 tcpdump -i any -p   -U -w - -s 0 -s 3000 -s 3000 by -s 3000
[07-25 17:05:05.893] (   217) [ 69350]     523     539  [tcpdump_c] set [-s 3000]
[07-25 17:05:06.324] (   139) [ 69350]     531     531  create first file
[07-25 17:05:06.345] (   140) [ 69350]     531     531  get new  file name(new logfile) : /data/ylog/ap/000-0725_170506.ylog 
[07-25 17:05:06.345] (   141) [ 69350]     531     531  open log file:/data/ylog/ap/000-0725_170506.ylog fd:23 diskfree:40767
[07-25 17:05:06.351] (   142) [ 69350]     531     531  update UID file:/data/ylog/loguid=0
[07-25 17:05:06.357] (   218) [ 69350]     523     528  get notifyNewFileHeader  but mBinHadStarted=1 ,so ignore
[07-25 17:05:11.598] (   219) [ 69355]     523     535  [uboot] set [0]
[07-25 17:05:11.722] (   220) [ 69356]     523     535  [lastlog] set [0]
[07-25 17:05:11.845] (   221) [ 69356]     523     535  [kernel] set [0]
[07-25 17:05:11.867] (   222) [ 69356]     523     535  [android] set [0]
[07-25 17:05:12.890] (   223) [ 69357]     523     535  [hcidump] set [0]
[07-25 17:05:13.910] (   224) [ 69358]     523     535  [tcpdump] set [0]
[07-25 17:05:13.917] (   225) [ 69358]     523     535  __ERROR  [audiodump]close source  failed<cmd> error  [4(Interrupted system call)] UnListenLogSource
[07-25 17:05:13.922] (   226) [ 69358]     523     539  rcv disable cmd , cur enable=1
[07-25 17:05:13.922] (   227) [ 69358]     523     539  [status] set [disable]
[07-25 17:05:13.924] (   228) [ 69358]     523     539  cmd set  LogEnable=0
[07-25 17:05:15.044] (   229) [ 69359]     523     535  sync Log to file
[07-25 17:05:15.044] (   230) [ 69359]     523     535  mSyncLog:1
[07-25 17:05:15.044] (   231) [ 69359]     523   26715  sublog recv disable cmd
[07-25 17:05:15.044] (   232) [ 69359]     523   26715  retryCnt:10, mSyncLog:1
[07-25 17:05:15.044] (   233) [ 69359]     523   26715  no data to sync
[07-25 17:05:15.045] (   234) [ 69359]     523   26715  no data to sync
[07-25 17:05:15.045] (   235) [ 69359]     523   26715  no data to sync
[07-25 17:05:15.046] (   236) [ 69359]     523   26715  sync 1590 log to file
[07-25 17:05:15.046] (   237) [ 69359]     523   26715  /data/ylog/ap/hcidump/002_0725_170505_hcidump.cap rename /data/ylog/ap/hcidump/002_0725_170505_0725_170515_hcidump.cap tmp:/data/ylog/ap/hcidump/002_0725_170505_
[07-25 17:05:15.046] (   238) [ 69359]     523   26715  /data/ylog/ap/tcpdump/002_0725_170505_tcpdump.cap rename /data/ylog/ap/tcpdump/002_0725_170505_0725_170515_tcpdump.cap tmp:/data/ylog/ap/tcpdump/002_0725_170505_
[07-25 17:05:15.117] (   143) [ 69359]     531     531  file renamed /data/ylog/ap/000-0725_170506.ylog to /data/ylog/ap/000-0725_170506--0725_170515.ylog
[07-25 17:05:15.117] (   144) [ 69359]     531     531  log file closed(close): /data/ylog/ap/000-0725_170506--0725_170515.ylog fd:23 size:1681765
[07-25 17:05:15.118] (   239) [ 69359]     523     539  gYlogStatus:0
[07-25 17:05:22.450] (   240) [ 69366]     523     539  rcv clear cmd
[07-25 17:05:22.450] (   241) [ 69366]     523     539  mYlogClear:1
[07-25 17:05:22.450] (   145) [ 69366]     531     531  logfilewriter rcv clear cmd clear
[07-25 17:05:22.452] (   146) [ 69366]     531     531  clear rmdir:/cache/ylog/ap/history/
[07-25 17:05:22.452] (   147) [ 69366]     531     531  clear rmdir:/data/ylog/ap/history/
[07-25 17:05:22.453] (   148) [ 69366]     531     531  clear rmdir:/storage/sdcard0/ylog/ap/history/
[07-25 17:05:22.453] (   149) [ 69366]     531     531  clear rmdir:/storage/emulated/0/ylog/ap/history/
[07-25 17:05:22.454] (   150) [ 69366]     531     531  clear rmdir:/data/ylog/ap/history/
[07-25 17:05:22.454] (   151) [ 69366]     531     531  clear rmdir:/cache/ylog/last_ylog/
[07-25 17:05:22.454] (   152) [ 69366]     531     531  clear rmdir:/data/ylog/last_ylog/
[07-25 17:05:22.454] (   153) [ 69366]     531     531  clear rmdir:/storage/sdcard0/ylog/last_ylog/
[07-25 17:05:22.454] (   154) [ 69366]     531     531  clear rmdir:/storage/emulated/0/ylog/last_ylog/
[07-25 17:05:22.455] (   155) [ 69366]     531     531  clear rmdir:/data/ylog/last_ylog/
[07-25 17:05:22.455] (   156) [ 69366]     531     531  clear rmdir:/cache/ylog/SYSDUMP/
[07-25 17:05:22.455] (   157) [ 69366]     531     531  clear rmdir:/data/ylog/SYSDUMP/
[07-25 17:05:22.455] (   158) [ 69366]     531     531  clear rmdir:/storage/sdcard0/ylog/SYSDUMP/
[07-25 17:05:22.455] (   159) [ 69366]     531     531  clear rmdir:/storage/emulated/0/ylog/SYSDUMP/
[07-25 17:05:22.456] (   160) [ 69366]     531     531  clear rmdir:/data/ylog/SYSDUMP/
[07-25 17:05:22.457] (   161) [ 69366]     531     531  clear rmdir:/cache/ylog/ap/current/
[07-25 17:05:22.457] (   162) [ 69366]     531     531  clear rmdir:/data/ylog/ap/current/
[07-25 17:05:22.457] (   163) [ 69366]     531     531  clear rmdir:/storage/sdcard0/ylog/ap/current/
[07-25 17:05:22.457] (   164) [ 69366]     531     531  clear rmdir:/storage/emulated/0/ylog/ap/current/
[07-25 17:05:22.459] (   165) [ 69366]     531     531  clear rmdir:/data/ylog/ap/current/
[07-25 17:05:22.459] (   166) [ 69366]     531     531  clear rmdir:/cache//ylog/poweron/ap/
[07-25 17:05:22.459] (   167) [ 69366]     531     531  clear rmdir:/data//ylog/poweron/ap/
[07-25 17:05:22.459] (   168) [ 69366]     531     531  clear rmdir:/storage/sdcard0//ylog/poweron/ap/
[07-25 17:05:22.459] (   169) [ 69366]     531     531  clear rmdir:/storage/emulated/0//ylog/poweron/ap/
[07-25 17:05:22.460] (   170) [ 69366]     531     531  clear rmdir:/data//ylog/poweron/ap/
[07-25 17:05:22.460] (   171) [ 69366]     531     531  clear rmdir:/cache//slog/
[07-25 17:05:22.460] (   172) [ 69366]     531     531  clear rmdir:/data//slog/
[07-25 17:05:22.460] (   173) [ 69366]     531     531  clear rmdir:/storage/sdcard0//slog/
[07-25 17:05:22.461] (   174) [ 69366]     531     531  clear rmdir:/storage/emulated/0//slog/
[07-25 17:05:22.461] (   175) [ 69366]     531     531  clear rmdir:/data//slog/
[07-25 17:05:22.462] (   176) [ 69366]     531     531  clear rmdir:/cache//ylog/ap/SYSDUMP/
[07-25 17:05:22.462] (   177) [ 69366]     531     531  clear rmdir:/data//ylog/ap/SYSDUMP/
[07-25 17:05:22.462] (   178) [ 69366]     531     531  clear rmdir:/storage/sdcard0//ylog/ap/SYSDUMP/
[07-25 17:05:22.462] (   179) [ 69366]     531     531  clear rmdir:/storage/emulated/0//ylog/ap/SYSDUMP/
[07-25 17:05:22.464] (   180) [ 69366]     531     531  clear rmdir:/data//ylog/ap/SYSDUMP/
[07-25 17:05:22.464] (   181) [ 69366]     531     531  clear rmdir:/cache/ylog/ap/
[07-25 17:05:22.464] (   182) [ 69366]     531     531  clear rmdir:/data/ylog/ap/
[07-25 17:05:22.470] (   183) [ 69366]     531     531  clear rmdir:/storage/sdcard0/ylog/ap/
[07-25 17:05:22.470] (   184) [ 69366]     531     531  clear rmdir:/storage/emulated/0/ylog/ap/
[07-25 17:05:22.474] (   185) [ 69366]     531     531  clear rmdir:/data/ylog/ap/
[07-25 17:05:22.474] (   186) [ 69366]     531     531  clear rmfile:/cache/ylog/ylogtrace.info
[07-25 17:05:22.474] (   187) [ 69366]     531     531  clear rmfile:/cache/ylog/fwcrash.info
[07-25 17:05:22.474] (   188) [ 69366]     531     531  clear rmfile:/data/ylog/ylogtrace.info
[07-25 17:05:22.474] (   189) [ 69366]     531     531  clear rmfile:/data/ylog/fwcrash.info
[07-25 17:05:22.474] (   190) [ 69366]     531     531  clear rmfile:/storage/sdcard0/ylog/ylogtrace.info
[07-25 17:05:22.474] (   191) [ 69366]     531     531  clear rmfile:/storage/sdcard0/ylog/fwcrash.info
[07-25 17:05:22.474] (   192) [ 69366]     531     531  clear rmfile:/storage/emulated/0/ylog/ylogtrace.info
[07-25 17:05:22.475] (   193) [ 69366]     531     531  clear rmfile:/storage/emulated/0/ylog/fwcrash.info
[07-25 17:05:22.476] (   194) [ 69366]     531     531  clear rmfile:/data/ylog/ylogtrace.info
[07-25 17:05:22.476] (   195) [ 69366]     531     531  clear rmfile:/data/ylog/fwcrash.info
[07-25 17:05:22.480] (   196) [ 69366]     531     531  uniqueID file deleted(/data/ylog/loguid) ret=0 
[07-25 17:05:22.480] (   197) [ 69366]     531     531  clear log take 0s
[07-25 17:05:24.624] (   242) [ 69368]     523     539  rcv enable cmd , cur enable=0
[07-25 17:05:24.625] (   243) [ 69368]     523     539  [status] set [enable]
[07-25 17:05:24.626] (   244) [ 69368]     523     539  cmd set  LogEnable=1
[07-25 17:05:24.626] (   245) [ 69368]     523     539  gYlogStatus:1
[07-25 17:05:24.626] (   246) [ 69368]     523     539  startlogServcie LogEnable : 1
[07-25 17:05:24.627] (   247) [ 69368]     523     539  clear sublog params:13
[07-25 17:05:24.627] (   248) [ 69368]     523     539  setSubLog onOff:0 logType:lastlog
[07-25 17:05:24.627] (   249) [ 69368]     523     539  setSubLog onOff:0 logType:uboot
[07-25 17:05:24.627] (   250) [ 69368]     523     539  setSubLog onOff:0 logType:android
[07-25 17:05:24.627] (   251) [ 69368]     523     539  setSubLog onOff:0 logType:kernel
[07-25 17:05:24.627] (   252) [ 69368]     523     539  setSubLog onOff:0 logType:trace
[07-25 17:05:24.627] (   253) [ 69368]     523     539  setSubLog onOff:0 logType:sgm
[07-25 17:05:24.627] (   254) [ 69368]     523     539  setSubLog onOff:0 logType:sysinfo
[07-25 17:05:24.627] (   255) [ 69368]     523     539  setSubLog onOff:0 logType:thermal
[07-25 17:05:24.627] (   256) [ 69368]     523     539  setSubLog onOff:0 logType:ylogdebug
[07-25 17:05:24.627] (   257) [ 69368]     523     539  setSubLog onOff:0 logType:phoneinfo
[07-25 17:05:24.627] (   258) [ 69368]     523     539  setSubLog onOff:1 logType:hcidump
[07-25 17:05:24.627] (   259) [ 69368]     523     539  [tcpdump] set bin to [tcpdump -i any -p   -U -w - -s 0 -s 3000]
[07-25 17:05:24.627] (   260) [ 69368]     523     539  setSubLog onOff:1 logType:tcpdump
[07-25 17:05:24.627] (   261) [ 69368]     523     539  setSubLog onOff:0 logType:trustlog
[07-25 17:05:24.634] (   262) [ 69368]     523     539  syncLegcyConfig
[07-25 17:05:24.634] (   263) [ 69368]     523     539  SubLog OnOff:0, logType lastlog is 0
[07-25 17:05:24.634] (   264) [ 69368]     523     539  SubLog OnOff:0, logType uboot is 0
[07-25 17:05:24.634] (   265) [ 69368]     523     539  SubLog OnOff:0, logType android is 0
[07-25 17:05:24.634] (   266) [ 69368]     523     539  SubLog OnOff:0, logType kernel is 0
[07-25 17:05:24.634] (   267) [ 69368]     523     539  SubLog OnOff:0, logType trace is 1
[07-25 17:05:24.634] (   268) [ 69368]     523     539  SubLog OnOff:0, logType sgm is 1
[07-25 17:05:24.634] (   269) [ 69368]     523     539  SubLog OnOff:0, logType sysinfo is 1
[07-25 17:05:24.634] (   270) [ 69368]     523     539  SubLog OnOff:0, logType thermal is 0
[07-25 17:05:24.634] (   271) [ 69368]     523     539  SubLog OnOff:0, logType ylogdebug is 1
[07-25 17:05:24.634] (   272) [ 69368]     523     539  SubLog OnOff:0, logType phoneinfo is 1
[07-25 17:05:24.634] (   273) [ 69368]     523     539  SubLog OnOff:1, logType hcidump is 0
[07-25 17:05:24.635] (   274) [ 69368]     523     539  SubLog OnOff:1, logType tcpdump is 0
[07-25 17:05:24.635] (   275) [ 69368]     523     539  SubLog OnOff:0, logType trustlog is 1
[07-25 17:05:24.635] (   276) [ 69368]     523     539  index:11,logType:tcpdump, logSize:256, totalSize:4096
[07-25 17:05:24.635] (   277) [ 69368]     523     539  index:10,logType:hcidump, logSize:64, totalSize:1024
[07-25 17:05:24.635] (   278) [ 69368]     523     539  value:default
[07-25 17:05:24.635] (   279) [ 69368]     523     539  make dir:/data/ylog/ap/
[07-25 17:05:24.635] (   280) [ 69368]     523     539  clearLogList remove  ptr:0xb400006f45994630
[07-25 17:05:24.636] (   281) [ 69368]     523     539  clearLogList remove  ptr:0xb400006f45993b30
[07-25 17:05:24.636] (   282) [ 69368]     523     539  mSubLog:1
[07-25 17:05:24.636] (   283) [ 69368]     523     539  mSubLog:1
[07-25 17:05:24.637] (   284) [ 69368]     523     535  [lastlog]  configure is [0]
[07-25 17:05:24.637] (   285) [ 69368]     523     535  [uboot]  configure is [0]
[07-25 17:05:24.637] (   286) [ 69368]     523     535  [android]  configure is [0]
[07-25 17:05:24.637] (   287) [ 69368]     523     535  [kernel]  configure is [0]
[07-25 17:05:24.637] (   288) [ 69368]     523     535  [trace]  configure is [1]
[07-25 17:05:24.640] (   289) [ 69368]     523     535  [sgm]  configure is [1]
[07-25 17:05:24.642] (   290) [ 69368]     523     535  [sysinfo]  configure is [1]
[07-25 17:05:24.655] (   291) [ 69368]     523     535  [thermal]  configure is [0]
[07-25 17:05:24.655] (   292) [ 69368]     523     535  [ylogdebug]  configure is [1]
[07-25 17:05:24.679] (   293) [ 69368]     523     535  [phoneinfo]  configure is [1]
[07-25 17:05:24.725] (   294) [ 69369]     523     535  [hcidump]  configure is [0]
[07-25 17:05:24.725] (   295) [ 69369]     523     535  [tcpdump]  configure is [0]
[07-25 17:05:24.725] (   296) [ 69369]     523     535  [trustlog]  configure is [1]
[07-25 17:05:24.754] (   297) [ 69369]     523     535  listen to 6 source 
[07-25 17:05:24.815] (   298) [ 69369]     523     535  ListenLogSource setValue:uboot
[07-25 17:05:24.816] (   299) [ 69369]     523     535  [uboot] set [1]
[07-25 17:05:24.876] (   300) [ 69369]     523     535  ListenLogSource setValue:lastlog
[07-25 17:05:24.876] (   301) [ 69369]     523     535  [lastlog] set [1]
[07-25 17:05:24.935] (   302) [ 69369]     523     535  ListenLogSource setValue:kernel
[07-25 17:05:24.935] (   303) [ 69369]     523     535  [kernel] set [1]
[07-25 17:05:24.974] (   304) [ 69369]     523     535  ListenLogSource setValue:android
[07-25 17:05:24.974] (   305) [ 69369]     523     535  [android] set [1]
[07-25 17:05:24.991] (   306) [ 69369]     523     535  ListenLogSource setValue:hcidump
[07-25 17:05:24.991] (   307) [ 69369]     523     535  [hcidump] set [1]
[07-25 17:05:25.080] (   308) [ 69369]     523     535  ListenLogSource setValue:tcpdump
[07-25 17:05:25.080] (   309) [ 69369]     523     535  [tcpdump] set [1]
[07-25 17:05:25.087] (   310) [ 69369]     523     535  __ERROR  [audiodump]close source  failed<cmd> error  [2(No such file or directory)] UnListenLogSource
[07-25 17:05:25.096] (   311) [ 69369]     523     539  [tcpdump]bin file changed2 tcpdump -i any -p   -U -w - -s 0 -s 3000 -s 3000 by -s 3000
[07-25 17:05:25.096] (   312) [ 69369]     523     539  [tcpdump_c] set [-s 3000]
[07-25 17:05:25.261] (   198) [ 69369]     531     531  create first file
[07-25 17:05:25.269] (   199) [ 69369]     531     531  get new  file name(new logfile) : /data/ylog/ap/000-0725_170525.ylog 
[07-25 17:05:25.269] (   200) [ 69369]     531     531  open log file:/data/ylog/ap/000-0725_170525.ylog fd:23 diskfree:40767
[07-25 17:05:25.285] (   201) [ 69369]     531     531  update UID file:/data/ylog/loguid=0
[07-25 17:05:25.286] (   313) [ 69369]     523     528  get notifyNewFileHeader  but mBinHadStarted=1 ,so ignore
[07-25 17:05:25.515] (   314) [ 69369]     523     535  index:11 log buffer is null 
[07-25 17:05:25.518] (   315) [ 69369]     523     535  index:12 log buffer is null 
[07-25 17:05:25.521] (   316) [ 69369]     523   26902  make dir /data/ylog/ap/tcpdump/
[07-25 17:05:25.553] (   317) [ 69369]     523   26902  open new log:/data/ylog/ap/tcpdump/003_0725_170525_tcpdump.cap, wfd:35, logname:/data/ylog/ap/tcpdump/003_0725_170525_tcpdump.cap
[07-25 17:05:25.557] (   318) [ 69369]     523   26902  logType->totalwriten:0 sublogsize:0
[07-25 17:06:00.087] (   319) [ 69404]     523     535  [uboot] set [0]
[07-25 17:06:00.200] (   320) [ 69404]     523     535  [lastlog] set [0]
[07-25 17:06:00.308] (   321) [ 69404]     523     535  [kernel] set [0]
[07-25 17:06:00.322] (   322) [ 69404]     523     535  [android] set [0]
[07-25 17:06:01.331] (   323) [ 69405]     523     535  [hcidump] set [0]
[07-25 17:06:02.344] (   324) [ 69406]     523     535  [tcpdump] set [0]
[07-25 17:06:02.351] (   325) [ 69406]     523     535  __ERROR  [audiodump]close source  failed<cmd> error  [4(Interrupted system call)] UnListenLogSource
[07-25 17:06:02.354] (   326) [ 69406]     523     539  rcv disable cmd , cur enable=1
[07-25 17:06:02.354] (   327) [ 69406]     523     539  [status] set [disable]
[07-25 17:06:02.356] (   328) [ 69406]     523     539  cmd set  LogEnable=0
[07-25 17:06:03.470] (   329) [ 69407]     523     535  sync Log to file
[07-25 17:06:03.471] (   330) [ 69407]     523     535  mSyncLog:1
[07-25 17:06:03.471] (   331) [ 69407]     523   26902  sublog recv disable cmd
[07-25 17:06:03.471] (   332) [ 69407]     523   26902  retryCnt:10, mSyncLog:1
[07-25 17:06:03.471] (   333) [ 69407]     523   26902  no data to sync
[07-25 17:06:03.472] (   334) [ 69407]     523   26902  sync 964 log to file
[07-25 17:06:03.472] (   335) [ 69407]     523   26902  close hcidump.cap failed.
[07-25 17:06:03.472] (   336) [ 69407]     523   26902  /data/ylog/ap/tcpdump/003_0725_170525_tcpdump.cap rename /data/ylog/ap/tcpdump/003_0725_170525_0725_170603_tcpdump.cap tmp:/data/ylog/ap/tcpdump/003_0725_170525_
[07-25 17:06:03.493] (   202) [ 69407]     531     531  file renamed /data/ylog/ap/000-0725_170525.ylog to /data/ylog/ap/000-0725_170525--0725_170603.ylog
[07-25 17:06:03.493] (   203) [ 69407]     531     531  log file closed(close): /data/ylog/ap/000-0725_170525--0725_170603.ylog fd:23 size:2270730
[07-25 17:06:03.494] (   337) [ 69407]     523     539  gYlogStatus:0
[07-25 17:06:31.111] (   338) [ 69435]     523     532  
 1127, 1074, 1367, 2730, 1884, 5192, 1049, 1086, 1378, 1214, 1381, 1841, 1149, 1083, 1383, 1119, 1326, 1103, 1343, 1067, 1403, 1109, 1336, 1061, 1050, 1378, 1060, 1361, 1063, 1372, 1102, 1356, 1087, 1365, 1112, 1376, 1106, 1099, 1382, 1077, 1389, 1069, 1334, 1081, 1390, 1098, 1343, 1110, 1087, 1343, 1130, 1346, 1054, 1349, 1094, 1388, 1054, 1388, 1083, 1357,
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
[07-26 07:47:17.233] (   339) [ 73036]     523     532  
 1127, 1074, 1367,    0,    0, 5192, 1049,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 1106, 1099, 1382,    0,    0,    0,    0,    0,    0,    0,    0,    0, 1087, 1343, 1130, 1346, 1054, 1349,    0, 1388, 1054, 1388, 1083, 1357,
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
[07-26 10:27:26.952] (   340) [ 73981]     523     539  rcv enable cmd , cur enable=0
[07-26 10:27:26.952] (   341) [ 73981]     523     539  [status] set [enable]
[07-26 10:27:26.957] (   342) [ 73981]     523     539  cmd set  LogEnable=1
[07-26 10:27:26.957] (   343) [ 73981]     523     539  gYlogStatus:1
[07-26 10:27:26.957] (   344) [ 73981]     523     539  startlogServcie LogEnable : 1
[07-26 10:27:26.961] (   345) [ 73981]     523     539  clear sublog params:13
[07-26 10:27:26.961] (   346) [ 73981]     523     539  setSubLog onOff:0 logType:lastlog
[07-26 10:27:26.962] (   347) [ 73981]     523     539  setSubLog onOff:0 logType:uboot
[07-26 10:27:26.962] (   348) [ 73981]     523     539  setSubLog onOff:0 logType:android
[07-26 10:27:26.962] (   349) [ 73981]     523     539  setSubLog onOff:0 logType:kernel
[07-26 10:27:26.962] (   350) [ 73981]     523     539  setSubLog onOff:0 logType:trace
[07-26 10:27:26.962] (   351) [ 73981]     523     539  setSubLog onOff:0 logType:sgm
[07-26 10:27:26.962] (   352) [ 73981]     523     539  setSubLog onOff:0 logType:sysinfo
[07-26 10:27:26.962] (   353) [ 73981]     523     539  setSubLog onOff:0 logType:thermal
[07-26 10:27:26.962] (   354) [ 73981]     523     539  setSubLog onOff:0 logType:ylogdebug
[07-26 10:27:26.962] (   355) [ 73981]     523     539  setSubLog onOff:0 logType:phoneinfo
[07-26 10:27:26.962] (   356) [ 73981]     523     539  setSubLog onOff:1 logType:hcidump
[07-26 10:27:26.962] (   357) [ 73981]     523     539  [tcpdump] set bin to [tcpdump -i any -p   -U -w - -s 0 -s 3000]
[07-26 10:27:26.962] (   358) [ 73981]     523     539  setSubLog onOff:1 logType:tcpdump
[07-26 10:27:26.962] (   359) [ 73981]     523     539  setSubLog onOff:0 logType:trustlog
[07-26 10:27:26.970] (   360) [ 73981]     523     539  syncLegcyConfig
[07-26 10:27:26.971] (   361) [ 73981]     523     539  SubLog OnOff:0, logType lastlog is 0
[07-26 10:27:26.971] (   362) [ 73981]     523     539  SubLog OnOff:0, logType uboot is 0
[07-26 10:27:26.971] (   363) [ 73981]     523     539  SubLog OnOff:0, logType android is 0
[07-26 10:27:26.971] (   364) [ 73981]     523     539  SubLog OnOff:0, logType kernel is 0
[07-26 10:27:26.971] (   365) [ 73981]     523     539  SubLog OnOff:0, logType trace is 1
[07-26 10:27:26.971] (   366) [ 73981]     523     539  SubLog OnOff:0, logType sgm is 1
[07-26 10:27:26.971] (   367) [ 73981]     523     539  SubLog OnOff:0, logType sysinfo is 1
[07-26 10:27:26.971] (   368) [ 73981]     523     539  SubLog OnOff:0, logType thermal is 0
[07-26 10:27:26.971] (   369) [ 73981]     523     539  SubLog OnOff:0, logType ylogdebug is 1
[07-26 10:27:26.971] (   370) [ 73981]     523     539  SubLog OnOff:0, logType phoneinfo is 1
[07-26 10:27:26.971] (   371) [ 73981]     523     539  SubLog OnOff:1, logType hcidump is 0
[07-26 10:27:26.971] (   372) [ 73981]     523     539  SubLog OnOff:1, logType tcpdump is 0
[07-26 10:27:26.971] (   373) [ 73981]     523     539  SubLog OnOff:0, logType trustlog is 1
[07-26 10:27:26.971] (   374) [ 73981]     523     539  index:11,logType:tcpdump, logSize:256, totalSize:4096
[07-26 10:27:26.971] (   375) [ 73981]     523     539  index:10,logType:hcidump, logSize:64, totalSize:1024
[07-26 10:27:26.971] (   376) [ 73981]     523     539  value:default
[07-26 10:27:26.973] (   377) [ 73981]     523     539  clearLogList remove  ptr:0xb400006f45993b30
[07-26 10:27:26.973] (   378) [ 73981]     523     539  clearLogList remove  ptr:0xb400006f45994630
[07-26 10:27:26.974] (   379) [ 73981]     523     539  mSubLog:1
[07-26 10:27:26.974] (   380) [ 73981]     523     539  mSubLog:1
[07-26 10:27:26.976] (   381) [ 73981]     523     535  [lastlog]  configure is [0]
[07-26 10:27:26.976] (   382) [ 73981]     523     535  [uboot]  configure is [0]
[07-26 10:27:26.976] (   383) [ 73981]     523     535  [android]  configure is [0]
[07-26 10:27:26.976] (   384) [ 73981]     523     535  [kernel]  configure is [0]
[07-26 10:27:26.976] (   385) [ 73981]     523     535  [trace]  configure is [1]
[07-26 10:27:26.985] (   386) [ 73981]     523     535  [sgm]  configure is [1]
[07-26 10:27:26.988] (   387) [ 73981]     523     535  [sysinfo]  configure is [1]
[07-26 10:27:26.997] (   388) [ 73981]     523     535  [thermal]  configure is [0]
[07-26 10:27:26.997] (   389) [ 73981]     523     535  [ylogdebug]  configure is [1]
[07-26 10:27:27.017] (   390) [ 73981]     523     535  [phoneinfo]  configure is [1]
[07-26 10:27:27.075] (   391) [ 73981]     523     535  [hcidump]  configure is [0]
[07-26 10:27:27.075] (   392) [ 73981]     523     535  [tcpdump]  configure is [0]
[07-26 10:27:27.075] (   393) [ 73981]     523     535  [trustlog]  configure is [1]
[07-26 10:27:27.099] (   394) [ 73981]     523     535  listen to 6 source 
[07-26 10:27:27.158] (   395) [ 73981]     523     535  ListenLogSource setValue:uboot
[07-26 10:27:27.158] (   396) [ 73981]     523     535  [uboot] set [1]
[07-26 10:27:27.207] (   397) [ 73981]     523     535  ListenLogSource setValue:lastlog
[07-26 10:27:27.207] (   398) [ 73981]     523     535  [lastlog] set [1]
[07-26 10:27:27.248] (   399) [ 73981]     523     535  ListenLogSource setValue:kernel
[07-26 10:27:27.248] (   400) [ 73981]     523     535  [kernel] set [1]
[07-26 10:27:27.270] (   401) [ 73981]     523     535  ListenLogSource setValue:android
[07-26 10:27:27.270] (   402) [ 73981]     523     535  [android] set [1]
[07-26 10:27:27.299] (   403) [ 73981]     523     535  ListenLogSource setValue:hcidump
[07-26 10:27:27.299] (   404) [ 73981]     523     535  [hcidump] set [1]
[07-26 10:27:27.358] (   405) [ 73981]     523     535  ListenLogSource setValue:tcpdump
[07-26 10:27:27.358] (   406) [ 73981]     523     535  [tcpdump] set [1]
[07-26 10:27:27.372] (   407) [ 73981]     523     535  __ERROR  [audiodump]close source  failed<cmd> error  [2(No such file or directory)] UnListenLogSource
[07-26 10:27:27.375] (   408) [ 73981]     523     539  [tcpdump]bin file changed2 tcpdump -i any -p   -U -w - -s 0 -s 3000 -s 3000 by -s 3000
[07-26 10:27:27.375] (   409) [ 73981]     523     539  [tcpdump_c] set [-s 3000]
[07-26 10:27:27.472] (   204) [ 73981]     531     531  create first file
[07-26 10:27:27.495] (   205) [ 73982]     531     531  get new  file name(new logfile) : /data/ylog/ap/001-0726_102727.ylog 
[07-26 10:27:27.496] (   206) [ 73982]     531     531  open log file:/data/ylog/ap/001-0726_102727.ylog fd:23 diskfree:40596
[07-26 10:27:27.501] (   207) [ 73982]     531     531  update UID file:/data/ylog/loguid=1
[07-26 10:27:27.503] (   410) [ 73982]     523     528  get notifyNewFileHeader  but mBinHadStarted=1 ,so ignore
[07-26 10:27:27.638] (   411) [ 73982]     523     535  index:11 log buffer is null 
[07-26 10:27:27.651] (   412) [ 73982]     523     535  index:12 log buffer is null 
[07-26 10:27:27.689] (   413) [ 73982]     523   11657  open new log:/data/ylog/ap/tcpdump/004_0726_102727_tcpdump.cap, wfd:35, logname:/data/ylog/ap/tcpdump/004_0726_102727_tcpdump.cap
[07-26 10:27:27.694] (   414) [ 73982]     523   11657  logType->totalwriten:0 sublogsize:0
[07-26 10:27:30.876] (   415) [ 73985]     523     535  [uboot] set [0]
[07-26 10:27:30.995] (   416) [ 73985]     523     535  [lastlog] set [0]
[07-26 10:27:31.110] (   417) [ 73985]     523     535  [kernel] set [0]
[07-26 10:27:31.131] (   418) [ 73985]     523     535  [android] set [0]
[07-26 10:27:32.143] (   419) [ 73986]     523     535  [hcidump] set [0]
[07-26 10:27:33.164] (   420) [ 73987]     523     535  [tcpdump] set [0]
[07-26 10:27:33.175] (   421) [ 73987]     523     535  __ERROR  [audiodump]close source  failed<cmd> error  [4(Interrupted system call)] UnListenLogSource
[07-26 10:27:33.181] (   422) [ 73987]     523     539  rcv disable cmd , cur enable=1
[07-26 10:27:33.181] (   423) [ 73987]     523     539  [status] set [disable]
[07-26 10:27:33.183] (   424) [ 73987]     523     539  cmd set  LogEnable=0
[07-26 10:27:34.310] (   425) [ 73988]     523     535  sync Log to file
[07-26 10:27:34.310] (   426) [ 73988]     523     535  mSyncLog:1
[07-26 10:27:34.310] (   427) [ 73988]     523   11657  sublog recv disable cmd
[07-26 10:27:34.310] (   428) [ 73988]     523   11657  retryCnt:10, mSyncLog:1
[07-26 10:27:34.310] (   429) [ 73988]     523   11657  no data to sync
[07-26 10:27:34.311] (   430) [ 73988]     523   11657  no data to sync
[07-26 10:27:34.311] (   431) [ 73988]     523   11657  sync 0 log to file
[07-26 10:27:34.311] (   432) [ 73988]     523   11657  close hcidump.cap failed.
[07-26 10:27:34.312] (   433) [ 73988]     523   11657  /data/ylog/ap/tcpdump/004_0726_102727_tcpdump.cap rename /data/ylog/ap/tcpdump/004_0726_102727_0726_102734_tcpdump.cap tmp:/data/ylog/ap/tcpdump/004_0726_102727_
[07-26 10:27:34.320] (   208) [ 73988]     531     531  file renamed /data/ylog/ap/001-0726_102727.ylog to /data/ylog/ap/001-0726_102727--0726_102734.ylog
[07-26 10:27:34.321] (   209) [ 73988]     531     531  log file closed(close): /data/ylog/ap/001-0726_102727--0726_102734.ylog fd:23 size:1412432
[07-26 10:27:34.321] (   434) [ 73988]     523     539  gYlogStatus:0
[07-26 10:33:12.048] (   435) [ 74082]     523     539  rcv clear cmd
[07-26 10:33:12.048] (   436) [ 74082]     523     539  mYlogClear:1
[07-26 10:33:12.048] (   210) [ 74082]     531     531  logfilewriter rcv clear cmd clear
[07-26 10:33:12.051] (   211) [ 74082]     531     531  clear rmdir:/cache/ylog/ap/history/
[07-26 10:33:12.052] (   212) [ 74082]     531     531  clear rmdir:/data/ylog/ap/history/
[07-26 10:33:12.052] (   213) [ 74082]     531     531  clear rmdir:/storage/sdcard0/ylog/ap/history/
[07-26 10:33:12.052] (   214) [ 74082]     531     531  clear rmdir:/storage/emulated/0/ylog/ap/history/
[07-26 10:33:12.054] (   215) [ 74082]     531     531  clear rmdir:/data/ylog/ap/history/
[07-26 10:33:12.054] (   216) [ 74082]     531     531  clear rmdir:/cache/ylog/last_ylog/
[07-26 10:33:12.054] (   217) [ 74082]     531     531  clear rmdir:/data/ylog/last_ylog/
[07-26 10:33:12.054] (   218) [ 74082]     531     531  clear rmdir:/storage/sdcard0/ylog/last_ylog/
[07-26 10:33:12.054] (   219) [ 74082]     531     531  clear rmdir:/storage/emulated/0/ylog/last_ylog/
[07-26 10:33:12.056] (   220) [ 74082]     531     531  clear rmdir:/data/ylog/last_ylog/
[07-26 10:33:12.056] (   221) [ 74082]     531     531  clear rmdir:/cache/ylog/SYSDUMP/
[07-26 10:33:12.056] (   222) [ 74082]     531     531  clear rmdir:/data/ylog/SYSDUMP/
[07-26 10:33:12.056] (   223) [ 74082]     531     531  clear rmdir:/storage/sdcard0/ylog/SYSDUMP/
[07-26 10:33:12.056] (   224) [ 74082]     531     531  clear rmdir:/storage/emulated/0/ylog/SYSDUMP/
[07-26 10:33:12.057] (   225) [ 74082]     531     531  clear rmdir:/data/ylog/SYSDUMP/
[07-26 10:33:12.057] (   226) [ 74082]     531     531  clear rmdir:/cache/ylog/ap/current/
[07-26 10:33:12.057] (   227) [ 74082]     531     531  clear rmdir:/data/ylog/ap/current/
[07-26 10:33:12.057] (   228) [ 74082]     531     531  clear rmdir:/storage/sdcard0/ylog/ap/current/
[07-26 10:33:12.057] (   229) [ 74082]     531     531  clear rmdir:/storage/emulated/0/ylog/ap/current/
[07-26 10:33:12.058] (   230) [ 74082]     531     531  clear rmdir:/data/ylog/ap/current/
[07-26 10:33:12.058] (   231) [ 74082]     531     531  clear rmdir:/cache//ylog/poweron/ap/
[07-26 10:33:12.058] (   232) [ 74082]     531     531  clear rmdir:/data//ylog/poweron/ap/
[07-26 10:33:12.058] (   233) [ 74082]     531     531  clear rmdir:/storage/sdcard0//ylog/poweron/ap/
[07-26 10:33:12.058] (   234) [ 74082]     531     531  clear rmdir:/storage/emulated/0//ylog/poweron/ap/
[07-26 10:33:12.059] (   235) [ 74082]     531     531  clear rmdir:/data//ylog/poweron/ap/
[07-26 10:33:12.059] (   236) [ 74082]     531     531  clear rmdir:/cache//slog/
[07-26 10:33:12.059] (   237) [ 74082]     531     531  clear rmdir:/data//slog/
[07-26 10:33:12.059] (   238) [ 74082]     531     531  clear rmdir:/storage/sdcard0//slog/
[07-26 10:33:12.059] (   239) [ 74082]     531     531  clear rmdir:/storage/emulated/0//slog/
[07-26 10:33:12.060] (   240) [ 74082]     531     531  clear rmdir:/data//slog/
[07-26 10:33:12.060] (   241) [ 74082]     531     531  clear rmdir:/cache//ylog/ap/SYSDUMP/
[07-26 10:33:12.060] (   242) [ 74082]     531     531  clear rmdir:/data//ylog/ap/SYSDUMP/
[07-26 10:33:12.060] (   243) [ 74082]     531     531  clear rmdir:/storage/sdcard0//ylog/ap/SYSDUMP/
[07-26 10:33:12.060] (   244) [ 74082]     531     531  clear rmdir:/storage/emulated/0//ylog/ap/SYSDUMP/
[07-26 10:33:12.061] (   245) [ 74082]     531     531  clear rmdir:/data//ylog/ap/SYSDUMP/
[07-26 10:33:12.061] (   246) [ 74082]     531     531  clear rmdir:/cache/ylog/ap/
[07-26 10:33:12.061] (   247) [ 74082]     531     531  clear rmdir:/data/ylog/ap/
[07-26 10:33:12.071] (   248) [ 74082]     531     531  clear rmdir:/storage/sdcard0/ylog/ap/
[07-26 10:33:12.071] (   249) [ 74082]     531     531  clear rmdir:/storage/emulated/0/ylog/ap/
[07-26 10:33:12.072] (   250) [ 74082]     531     531  clear rmdir:/data/ylog/ap/
[07-26 10:33:12.072] (   251) [ 74082]     531     531  clear rmfile:/cache/ylog/ylogtrace.info
[07-26 10:33:12.072] (   252) [ 74082]     531     531  clear rmfile:/cache/ylog/fwcrash.info
[07-26 10:33:12.072] (   253) [ 74082]     531     531  clear rmfile:/data/ylog/ylogtrace.info
[07-26 10:33:12.072] (   254) [ 74082]     531     531  clear rmfile:/data/ylog/fwcrash.info
[07-26 10:33:12.072] (   255) [ 74082]     531     531  clear rmfile:/storage/sdcard0/ylog/ylogtrace.info
[07-26 10:33:12.073] (   256) [ 74082]     531     531  clear rmfile:/storage/sdcard0/ylog/fwcrash.info
[07-26 10:33:12.073] (   257) [ 74082]     531     531  clear rmfile:/storage/emulated/0/ylog/ylogtrace.info
[07-26 10:33:12.073] (   258) [ 74082]     531     531  clear rmfile:/storage/emulated/0/ylog/fwcrash.info
[07-26 10:33:12.074] (   259) [ 74082]     531     531  clear rmfile:/data/ylog/ylogtrace.info
[07-26 10:33:12.074] (   260) [ 74082]     531     531  clear rmfile:/data/ylog/fwcrash.info
[07-26 10:33:12.078] (   261) [ 74082]     531     531  uniqueID file deleted(/data/ylog/loguid) ret=0 
[07-26 10:33:12.078] (   262) [ 74082]     531     531  clear log take 0s
[07-26 10:40:22.256] (   437) [ 74344]     523     539  rcv clear cmd
[07-26 10:40:22.256] (   438) [ 74344]     523     539  mYlogClear:1
[07-26 10:40:22.257] (   263) [ 74344]     531     531  logfilewriter rcv clear cmd clear
[07-26 10:40:22.259] (   264) [ 74344]     531     531  clear rmdir:/cache/ylog/ap/history/
[07-26 10:40:22.259] (   265) [ 74344]     531     531  clear rmdir:/data/ylog/ap/history/
[07-26 10:40:22.259] (   266) [ 74344]     531     531  clear rmdir:/storage/sdcard0/ylog/ap/history/
[07-26 10:40:22.260] (   267) [ 74344]     531     531  clear rmdir:/storage/emulated/0/ylog/ap/history/
[07-26 10:40:22.261] (   268) [ 74344]     531     531  clear rmdir:/data/ylog/ap/history/
[07-26 10:40:22.261] (   269) [ 74344]     531     531  clear rmdir:/cache/ylog/last_ylog/
[07-26 10:40:22.261] (   270) [ 74344]     531     531  clear rmdir:/data/ylog/last_ylog/
[07-26 10:40:22.261] (   271) [ 74344]     531     531  clear rmdir:/storage/sdcard0/ylog/last_ylog/
[07-26 10:40:22.261] (   272) [ 74344]     531     531  clear rmdir:/storage/emulated/0/ylog/last_ylog/
[07-26 10:40:22.262] (   273) [ 74344]     531     531  clear rmdir:/data/ylog/last_ylog/
[07-26 10:40:22.262] (   274) [ 74344]     531     531  clear rmdir:/cache/ylog/SYSDUMP/
[07-26 10:40:22.262] (   275) [ 74344]     531     531  clear rmdir:/data/ylog/SYSDUMP/
[07-26 10:40:22.262] (   276) [ 74344]     531     531  clear rmdir:/storage/sdcard0/ylog/SYSDUMP/
[07-26 10:40:22.262] (   277) [ 74344]     531     531  clear rmdir:/storage/emulated/0/ylog/SYSDUMP/
[07-26 10:40:22.263] (   278) [ 74344]     531     531  clear rmdir:/data/ylog/SYSDUMP/
[07-26 10:40:22.263] (   279) [ 74344]     531     531  clear rmdir:/cache/ylog/ap/current/
[07-26 10:40:22.263] (   280) [ 74344]     531     531  clear rmdir:/data/ylog/ap/current/
[07-26 10:40:22.263] (   281) [ 74344]     531     531  clear rmdir:/storage/sdcard0/ylog/ap/current/
[07-26 10:40:22.263] (   282) [ 74344]     531     531  clear rmdir:/storage/emulated/0/ylog/ap/current/
[07-26 10:40:22.264] (   283) [ 74344]     531     531  clear rmdir:/data/ylog/ap/current/
[07-26 10:40:22.264] (   284) [ 74344]     531     531  clear rmdir:/cache//ylog/poweron/ap/
[07-26 10:40:22.264] (   285) [ 74344]     531     531  clear rmdir:/data//ylog/poweron/ap/
[07-26 10:40:22.264] (   286) [ 74344]     531     531  clear rmdir:/storage/sdcard0//ylog/poweron/ap/
[07-26 10:40:22.264] (   287) [ 74344]     531     531  clear rmdir:/storage/emulated/0//ylog/poweron/ap/
[07-26 10:40:22.267] (   288) [ 74344]     531     531  clear rmdir:/data//ylog/poweron/ap/
[07-26 10:40:22.267] (   289) [ 74344]     531     531  clear rmdir:/cache//slog/
[07-26 10:40:22.267] (   290) [ 74344]     531     531  clear rmdir:/data//slog/
[07-26 10:40:22.267] (   291) [ 74344]     531     531  clear rmdir:/storage/sdcard0//slog/
[07-26 10:40:22.267] (   292) [ 74344]     531     531  clear rmdir:/storage/emulated/0//slog/
[07-26 10:40:22.268] (   293) [ 74344]     531     531  clear rmdir:/data//slog/
[07-26 10:40:22.268] (   294) [ 74344]     531     531  clear rmdir:/cache//ylog/ap/SYSDUMP/
[07-26 10:40:22.268] (   295) [ 74344]     531     531  clear rmdir:/data//ylog/ap/SYSDUMP/
[07-26 10:40:22.268] (   296) [ 74344]     531     531  clear rmdir:/storage/sdcard0//ylog/ap/SYSDUMP/
[07-26 10:40:22.269] (   297) [ 74344]     531     531  clear rmdir:/storage/emulated/0//ylog/ap/SYSDUMP/
[07-26 10:40:22.269] (   298) [ 74344]     531     531  clear rmdir:/data//ylog/ap/SYSDUMP/
[07-26 10:40:22.269] (   299) [ 74344]     531     531  clear rmdir:/cache/ylog/ap/
[07-26 10:40:22.269] (   300) [ 74344]     531     531  clear rmdir:/data/ylog/ap/
[07-26 10:40:22.269] (   301) [ 74344]     531     531  clear rmdir:/storage/sdcard0/ylog/ap/
[07-26 10:40:22.270] (   302) [ 74344]     531     531  clear rmdir:/storage/emulated/0/ylog/ap/
[07-26 10:40:22.273] (   303) [ 74344]     531     531  clear rmdir:/data/ylog/ap/
[07-26 10:40:22.273] (   304) [ 74344]     531     531  clear rmfile:/cache/ylog/ylogtrace.info
[07-26 10:40:22.273] (   305) [ 74344]     531     531  clear rmfile:/cache/ylog/fwcrash.info
[07-26 10:40:22.273] (   306) [ 74344]     531     531  clear rmfile:/data/ylog/ylogtrace.info
[07-26 10:40:22.273] (   307) [ 74344]     531     531  clear rmfile:/data/ylog/fwcrash.info
[07-26 10:40:22.273] (   308) [ 74344]     531     531  clear rmfile:/storage/sdcard0/ylog/ylogtrace.info
[07-26 10:40:22.273] (   309) [ 74344]     531     531  clear rmfile:/storage/sdcard0/ylog/fwcrash.info
[07-26 10:40:22.273] (   310) [ 74344]     531     531  clear rmfile:/storage/emulated/0/ylog/ylogtrace.info
[07-26 10:40:22.276] (   311) [ 74344]     531     531  clear rmfile:/storage/emulated/0/ylog/fwcrash.info
[07-26 10:40:22.277] (   312) [ 74344]     531     531  clear rmfile:/data/ylog/ylogtrace.info
[07-26 10:40:22.277] (   313) [ 74344]     531     531  clear rmfile:/data/ylog/fwcrash.info
[07-26 10:40:22.280] (   314) [ 74344]     531     531  uniqueID file deleted(/data/ylog/loguid) ret=-1 No such file or directory
[07-26 10:40:22.280] (   315) [ 74344]     531     531  clear log take 0s
[07-26 10:40:26.894] (   439) [ 74348]     523     539  rcv enable cmd , cur enable=0
[07-26 10:40:26.894] (   440) [ 74348]     523     539  [status] set [enable]
[07-26 10:40:26.901] (   441) [ 74348]     523     539  cmd set  LogEnable=1
[07-26 10:40:26.901] (   442) [ 74348]     523     539  gYlogStatus:1
[07-26 10:40:26.902] (   443) [ 74348]     523     539  startlogServcie LogEnable : 1
[07-26 10:40:26.902] (   444) [ 74348]     523     539  clear sublog params:13
[07-26 10:40:26.902] (   445) [ 74348]     523     539  setSubLog onOff:0 logType:lastlog
[07-26 10:40:26.902] (   446) [ 74348]     523     539  setSubLog onOff:0 logType:uboot
[07-26 10:40:26.902] (   447) [ 74348]     523     539  setSubLog onOff:0 logType:android
[07-26 10:40:26.903] (   448) [ 74348]     523     539  setSubLog onOff:0 logType:kernel
[07-26 10:40:26.903] (   449) [ 74348]     523     539  setSubLog onOff:0 logType:trace
[07-26 10:40:26.903] (   450) [ 74348]     523     539  setSubLog onOff:0 logType:sgm
[07-26 10:40:26.903] (   451) [ 74348]     523     539  setSubLog onOff:0 logType:sysinfo
[07-26 10:40:26.903] (   452) [ 74348]     523     539  setSubLog onOff:0 logType:thermal
[07-26 10:40:26.903] (   453) [ 74348]     523     539  setSubLog onOff:0 logType:ylogdebug
[07-26 10:40:26.903] (   454) [ 74348]     523     539  setSubLog onOff:0 logType:phoneinfo
[07-26 10:40:26.903] (   455) [ 74348]     523     539  setSubLog onOff:1 logType:hcidump
[07-26 10:40:26.903] (   456) [ 74348]     523     539  [tcpdump] set bin to [tcpdump -i any -p   -U -w - -s 0 -s 3000]
[07-26 10:40:26.903] (   457) [ 74348]     523     539  setSubLog onOff:1 logType:tcpdump
[07-26 10:40:26.903] (   458) [ 74348]     523     539  setSubLog onOff:0 logType:trustlog
[07-26 10:40:26.911] (   459) [ 74348]     523     539  syncLegcyConfig
[07-26 10:40:26.912] (   460) [ 74348]     523     539  SubLog OnOff:0, logType lastlog is 0
[07-26 10:40:26.912] (   461) [ 74348]     523     539  SubLog OnOff:0, logType uboot is 0
[07-26 10:40:26.912] (   462) [ 74348]     523     539  SubLog OnOff:0, logType android is 0
[07-26 10:40:26.912] (   463) [ 74348]     523     539  SubLog OnOff:0, logType kernel is 0
[07-26 10:40:26.912] (   464) [ 74348]     523     539  SubLog OnOff:0, logType trace is 1
[07-26 10:40:26.912] (   465) [ 74348]     523     539  SubLog OnOff:0, logType sgm is 1
[07-26 10:40:26.912] (   466) [ 74348]     523     539  SubLog OnOff:0, logType sysinfo is 1
[07-26 10:40:26.912] (   467) [ 74348]     523     539  SubLog OnOff:0, logType thermal is 0
[07-26 10:40:26.912] (   468) [ 74348]     523     539  SubLog OnOff:0, logType ylogdebug is 1
[07-26 10:40:26.912] (   469) [ 74348]     523     539  SubLog OnOff:0, logType phoneinfo is 1
[07-26 10:40:26.912] (   470) [ 74348]     523     539  SubLog OnOff:1, logType hcidump is 0
[07-26 10:40:26.912] (   471) [ 74348]     523     539  SubLog OnOff:1, logType tcpdump is 0
[07-26 10:40:26.912] (   472) [ 74348]     523     539  SubLog OnOff:0, logType trustlog is 1
[07-26 10:40:26.912] (   473) [ 74348]     523     539  index:11,logType:tcpdump, logSize:256, totalSize:4096
[07-26 10:40:26.912] (   474) [ 74348]     523     539  index:10,logType:hcidump, logSize:64, totalSize:1024
[07-26 10:40:26.912] (   475) [ 74348]     523     539  value:default
[07-26 10:40:26.912] (   476) [ 74348]     523     539  make dir:/data/ylog/ap/
[07-26 10:40:26.913] (   477) [ 74348]     523     539  clearLogList remove  ptr:0xb400006f45994630
[07-26 10:40:26.913] (   478) [ 74348]     523     539  clearLogList remove  ptr:0xb400006f45993b30
[07-26 10:40:26.913] (   479) [ 74348]     523     539  mSubLog:1
[07-26 10:40:26.913] (   480) [ 74348]     523     539  mSubLog:1
[07-26 10:40:26.914] (   481) [ 74348]     523     535  [lastlog]  configure is [0]
[07-26 10:40:26.914] (   482) [ 74348]     523     535  [uboot]  configure is [0]
[07-26 10:40:26.914] (   483) [ 74348]     523     535  [android]  configure is [0]
[07-26 10:40:26.915] (   484) [ 74348]     523     535  [kernel]  configure is [0]
[07-26 10:40:26.915] (   485) [ 74348]     523     535  [trace]  configure is [1]
[07-26 10:40:26.925] (   486) [ 74348]     523     535  [sgm]  configure is [1]
[07-26 10:40:26.929] (   487) [ 74348]     523     535  [sysinfo]  configure is [1]
[07-26 10:40:26.937] (   488) [ 74348]     523     535  [thermal]  configure is [0]
[07-26 10:40:26.937] (   489) [ 74348]     523     535  [ylogdebug]  configure is [1]
[07-26 10:40:26.966] (   490) [ 74348]     523     535  [phoneinfo]  configure is [1]
[07-26 10:40:26.994] (   491) [ 74348]     523     535  [hcidump]  configure is [0]
[07-26 10:40:26.994] (   492) [ 74348]     523     535  [tcpdump]  configure is [0]
[07-26 10:40:26.994] (   493) [ 74348]     523     535  [trustlog]  configure is [1]
[07-26 10:40:27.015] (   494) [ 74348]     523     535  listen to 6 source 
[07-26 10:40:27.071] (   495) [ 74348]     523     535  ListenLogSource setValue:uboot
[07-26 10:40:27.072] (   496) [ 74348]     523     535  [uboot] set [1]
[07-26 10:40:27.121] (   497) [ 74348]     523     535  ListenLogSource setValue:lastlog
[07-26 10:40:27.121] (   498) [ 74348]     523     535  [lastlog] set [1]
[07-26 10:40:27.180] (   499) [ 74348]     523     535  ListenLogSource setValue:kernel
[07-26 10:40:27.180] (   500) [ 74348]     523     535  [kernel] set [1]
[07-26 10:40:27.221] (   501) [ 74348]     523     535  ListenLogSource setValue:android
[07-26 10:40:27.221] (   502) [ 74348]     523     535  [android] set [1]
[07-26 10:40:27.272] (   503) [ 74349]     523     535  ListenLogSource setValue:hcidump
[07-26 10:40:27.272] (   504) [ 74349]     523     535  [hcidump] set [1]
[07-26 10:40:27.358] (   505) [ 74349]     523     535  ListenLogSource setValue:tcpdump
[07-26 10:40:27.358] (   506) [ 74349]     523     535  [tcpdump] set [1]
[07-26 10:40:27.373] (   507) [ 74349]     523     535  __ERROR  [audiodump]close source  failed<cmd> error  [2(No such file or directory)] UnListenLogSource
[07-26 10:40:27.376] (   316) [ 74349]     531     531  create first file
[07-26 10:40:27.376] (   508) [ 74349]     523     539  [tcpdump]bin file changed2 tcpdump -i any -p   -U -w - -s 0 -s 3000 -s 3000 by -s 3000
[07-26 10:40:27.376] (   509) [ 74349]     523     539  [tcpdump_c] set [-s 3000]
[07-26 10:40:27.386] (   317) [ 74349]     531     531  get new  file name(new logfile) : /data/ylog/ap/000-0726_104027.ylog 
[07-26 10:40:27.386] (   318) [ 74349]     531     531  open log file:/data/ylog/ap/000-0726_104027.ylog fd:23 diskfree:40660
[07-26 10:40:27.399] (   319) [ 74349]     531     531  update UID file:/data/ylog/loguid=0
[07-26 10:40:27.401] (   510) [ 74349]     523     528  get notifyNewFileHeader  but mBinHadStarted=1 ,so ignore
[07-26 10:40:27.661] (   511) [ 74349]     523     535  index:11 log buffer is null 
[07-26 10:40:27.664] (   512) [ 74349]     523     535  index:12 log buffer is null 
[07-26 10:40:27.683] (   513) [ 74349]     523   12281  make dir /data/ylog/ap/tcpdump/
[07-26 10:40:27.704] (   514) [ 74349]     523   12281  open new log:/data/ylog/ap/tcpdump/005_0726_104027_tcpdump.cap, wfd:35, logname:/data/ylog/ap/tcpdump/005_0726_104027_tcpdump.cap
[07-26 10:40:27.705] (   515) [ 74349]     523   12281  logType->totalwriten:0 sublogsize:0
run finished on 07-26 10:40:30
ylogdebug end




