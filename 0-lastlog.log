YZIPC02lastlog/kernel.log20CPIZY

YZIPC02lastlog/error.log20CPIZY

YZIPC02lastlog/android.log20CPIZY

YZIPC02lastlog/lastkmsg.log20CPIZY

(null)
/dev/blkio cgroup rw,nosuid,nodev,noexec,relatime,blkio 0 0
<31>[    7.538385][T397@C7] ylog:: none /sys/fs/cgroup cgroup2 rw,nosuid,nodev,noexec,relatime,memory_recursiveprot 0 0
<31>[    7.538457][T397@C7] ylog:: none /dev/cpuctl cgroup rw,nosuid,nodev,noexec,relatime,cpu 0 0
<31>[    7.538472][T397@C7] ylog:: none /dev/cpuset cgroup rw,nosuid,nodev,noexec,relatime,cpuset,noprefix,release_agent=/sbin/cpuset_release_agent 0 0
<31>[    7.538485][T397@C7] ylog:: none /dev/memcg cgroup rw,nosuid,nodev,noexec,relatime,memory 0 0
<31>[    7.538498][T397@C7] ylog:: tmpfs /linkerconfig tmpfs rw,seclabel,nosuid,nodev,noexec,relatime,mode=755 0 0
<31>[    7.538511][T397@C7] ylog:: /dev/block/loop3 /apex/com.android.tzdata@351400020 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
<31>[    7.538524][T397@C7] ylog:: /dev/block/loop3 /bootstrap-apex/com.android.tzdata@351400020 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
<31>[    7.538537][T397@C7] ylog:: /dev/block/loop0 /apex/com.android.vndk.v33@1 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
<31>[    7.538550][T397@C7] ylog:: /dev/block/loop0 /bootstrap-apex/com.android.vndk.v33@1 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
<31>[    7.538563][T397@C7] ylog:: /dev/block/loop1 /apex/com.android.runtime@1 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
<31>[    7.538576][T397@C7] ylog:: /dev/block/loop1 /bootstrap-apex/com.android.runtime@1 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
<31>[    7.538589][T397@C7] ylog:: /dev/block/loop2 /apex/com.android.i18n@1 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
<31>[    7.538649][T397@C7] ylog:: /dev/block/loop2 /bootstrap-apex/com.android.i18n@1 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
<31>[    7.538662][T397@C7] ylog:: /dev/block/loop0 /apex/com.android.vndk.v33 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
<31>[    7.538675][T397@C7] ylog:: /dev/block/loop0 /bootstrap-apex/com.android.vndk.v33 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
<31>[    7.538688][T397@C7] ylog:: /dev/block/loop3 /apex/com.android.tzdata ext4 ro,dirsync,seclabel,nodev,noatime 0 0
<31>[    7.538701][T397@C7] ylog:: /dev/block/loop3 /bootstrap-apex/com.android.tzdata ext4 ro,dirsync,seclabel,nodev,noatime 0 0
<31>[    7.538714][T397@C7] ylog:: /dev/block/loop1 /apex/com.android.runtime ext4 ro,dirsync,seclabel,nodev,noatime 0 0
<31>[    7.538728][T397@C7] ylog:: /dev/block/loop1 /bootstrap-apex/com.android.runtime ext4 ro,dirsync,seclabel,nodev,noatime 0 0
<31>[    7.538741][T397@C7] ylog:: /dev/block/loop2 /apex/com.android.i18n ext4 ro,dirsync,seclabel,nodev,noatime 0 0
<31>[    7.538754][T397@C7] ylog:: /dev/block/loop2 /bootstrap-apex/com.android.i18n ext4 ro,dirsync,seclabel,nodev,noatime 0 0
<31>[    7.538767][T397@C7] ylog:: tmpfs /apex/apex-info-list.xml tmpfs rw,seclabel,nosuid,nodev,noexec,relatime,mode=755 0 0
<31>[    7.538780][T397@C7] ylog:: tmpfs /bootstrap-apex/apex-info-list.xml tmpfs rw,seclabel,nosuid,nodev,noexec,relatime,mode=755 0 0
<31>[    7.538794][T397@C7] ylog:: tracefs /sys/kernel/tracing tracefs rw,seclabel,relatime,gid=3012 0 0
<31>[    7.538863][T397@C7] ylog:: tmpfs /tmp tmpfs rw,seclabel,relatime 0 0
<31>[    7.538876][T397@C7] ylog:: debugfs /sys/kernel/debug debugfs rw,seclabel,relatime 0 0
<31>[    7.538889][T397@C7] ylog:: none /config configfs rw,nosuid,nodev,noexec,relatime 0 0
<31>[    7.538902][T397@C7] ylog:: binder /dev/binderfs binder rw,relatime,max=1048576,stats=global 0 0
<31>[    7.538915][T397@C7] ylog:: none /sys/fs/fuse/connections fusectl rw,relatime 0 0
<31>[    7.538927][T397@C7] ylog:: bpf /sys/fs/bpf bpf rw,nosuid,nodev,noexec,relatime 0 0
<31>[    7.538940][T397@C7] ylog:: pstore /sys/fs/pstore pstore rw,seclabel,nosuid,nodev,noexec,relatime 0 0
<31>[    7.538953][T397@C7] ylog:: adb /dev/usb-ffs/adb functionfs rw,relatime 0 0
<31>[    7.538966][T397@C7] ylog:: mtp /dev/usb-ffs/mtp functionfs rw,relatime 0 0
<31>[    7.538978][T397@C7] ylog:: ptp /dev/usb-ffs/ptp functionfs rw,relatime 0 0
<31>[    7.538992][T397@C7] ylog:: /dev/block/mmcblk0p1 /mnt/vendor ext4 rw,seclabel,nosuid,nodev,noatime,noauto_da_alloc 0 0
<31>[    7.539007][T397@C7] ylog:: /dev/block/mmcblk0p47 /cache f2fs rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,alloc_mode=reuse,checkpoint
<31>[    7.539042][T397@C7] ylog:: /dev/block/mmcblk0p48 /blackbox f2fs rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,alloc_mode=reuse,checkpo
<31>[    7.539056][T397@C7] ylog:: tmpfs /data tmpfs rw,seclabel,relatime,mode=777 0 0
<31>[    7.539069][T397@C7] ylog:: tracefs /sys/kernel/debug/tracing tracefs rw,seclabel,relatime,gid=3012 0 0
<31>[    7.539083][T397@C7] ylog:: /dev/block/dm-10 /product/priv-app/Payjoy erofs ro,seclabel,relatime,user_xattr,acl,cache_strategy=readaround 0 0
<14>[    7.548210][T291@C6] servicemanager: Caller(pid=383,uid=1000,sid=u:r:hal_face_default:s0) Found android.hardware.biometrics.face.IFace/default in device VINTF manifest.
<6>[    7.554062][T10@C5] musb-sprd 64900000.usb: sm_work: idle state
<6>[    7.554100][T10@C5] musb-sprd 64900000.usb: waiting musb udc start
<31>[    7.554779][T423@C5] ylog:: [7] [[12-31 21:12:56.963] ]  [0] rd:00000000  wr:FFFFFFFF [0  0  0]
<6>[    7.556120][T55@C4] WCN BOOT: : BTWF: magic_value=0xf0f0f0f1, wait_count=16
<36>[    7.593165][T304@C3] type=1400 audit(776.995:19): avc:  denied  { write } for  comm="ylog.writer" name="/" dev="tmpfs" ino=1 scontext=u:r:ylog:s0 tcontext=u:object_r:tmpfs:s0 tclass=dir permissive=0
<6>[    7.598065][T160@C6] unisoc-mailbox channel 5-6 receive open msg!
<6>[    7.598083][T160@C6] unisoc-mailbox channel 5-6 success
<6>[    7.598094][T160@C6] sprd-sbuf: channel 5-6 state = 0, recv cmd msg, flag = 1!
<6>[    7.598116][T159@C7] unisoc-mailbox channel 5-4 receive open msg!
<6>[    7.598124][T355@C3] modem modem@0: start over
<6>[    7.598128][T159@C7] unisoc-mailbox channel 5-4 success
<6>[    7.598138][T159@C7] sprd-sbuf: channel 5-4 state = 0, recv cmd msg, flag = 1!
<14>[    7.598346][T293@C1] logd: logdr: UID=1000 GID=1000 PID=432 b tail=0 logMask=1f pid=0 start=0ns deadline=0ns
<6>[    7.598620][T355@C3] modem modem@1: modem_control modem run = 1!
<6>[    7.601027][T169@C7] unisoc-mailbox channel 5-5 receive open msg!
<6>[    7.601046][T169@C7] unisoc-mailbox channel 5-5 success
<6>[    7.601056][T169@C7] sprd-sblock: sblock thread recv msg: dst=5, channel=5, type=5, flag=0x0001, value=0x00000000
<6>[    7.601082][T161@C6] unisoc-mailbox channel 5-21 receive open msg!
<6>[    7.601090][T169@C7] sprd-sblock: send init done!
<6>[    7.601096][T161@C6] unisoc-mailbox channel 5-21 success
<6>[    7.601098][T169@C7] sprd-sblock: channel 5-5, SMSG_CMD_SBLOCK_INIT, dst address = 0x8e0a9000!
<6>[    7.601107][T161@C6] sprd-sbuf: channel 5-21 state = 0, recv cmd msg, flag = 1!
<6>[    7.614074][T10@C5] musb-sprd 64900000.usb: sm_work: idle state
<6>[    7.614112][T10@C5] musb-sprd 64900000.usb: waiting musb udc start
<6>[    7.639961][T354@C2] charger-manager charger-manager: charger_stop_store, stop_charge=1
<6>[    7.639996][T354@C2] vote_gov:VOTE_TYPE_CCCV vote the same vote_client[1].value = 0, value = 0; vote_client[1].enable = 0, enable = 0
<6>[    7.640017][T354@C2] charger-manager charger-manager: cm_ir_compensation_enable stop ir compensation
<6>[    7.642291][T354@C2] bq2560x_chg 5-006b: bq2560x_charger_stop_charge:line601: stop charge
<6>[    7.646097][T354@C2] charger-manager charger-manager: Release charger_manager_wakelock when disable charge
<3>[    7.648702][T340@C2] charger-manager charger-manager: Can not to get board temperature, return init_temp=250
<3>[    7.648738][T340@C2] power_supply battery: driver failed to report `temp_ambient' property: -22
<14>[    7.651480][ T1@C7] init: processing action (vendor.ro.bootmode=cali) from (/vendor/etc/init/autotest.rc:43)
<14>[    7.651870][ T1@C3] init: processing action (vendor.ro.bootmode=cali) from (/vendor/etc/init/dcxsrv.rc:7)
<14>[    7.652932][ T1@C7] init: starting service 'vendor.dcxsrv'...
<14>[    7.665046][ T1@C7] init: ... started service 'vendor.dcxsrv' has pid 451
<14>[    7.665320][ T1@C7] init: processing action (ft-mount-sdcard) from (/vendor/etc/init/autotest.rc:33)
<14>[    7.668764][ T1@C7] init: Command 'symlink /mnt/media_rw/87EE-18F4 /storage/87EE-18F4' action=ft-mount-sdcard (/vendor/etc/init/autotest.rc:37) took 0ms and failed: symlink() failed: Read-only file system
<14>[    7.669303][ T1@C7] init: processing action (vendor.flag.sys.usb.config=1 && vendor.ro.bootmode=cali) from (/vendor/etc/init/engpc.rc:10)
<6>[    7.670746][T10@C3] musb-sprd 64900000.usb: sm_work: idle state
<6>[    7.670781][T10@C3] musb-sprd 64900000.usb: waiting musb udc start
<14>[    7.671706][ T1@C4] init: processing action (sys.usb.config=vser && sys.usb.configfs=1) from (/vendor/etc/init/hw/init.ums9230_6h10.usb.rc:1111)
<31>[    7.674400][T422@C1] ylog:: set logdir to /blackbox/ylog/ap/, diskfree:382
<14>[    7.677619][ T1@C7] init: Command 'rm /config/usb_gadget/g1/configs/b.1/f1' action=sys.usb.config=vser && sys.usb.configfs=1 (/vendor/etc/init/hw/init.ums9230_6h10.usb.rc:1117) took 0ms and failed: unlink() failed: No such file or directory
<14>[    7.678044][ T1@C7] init: Command 'rm /config/usb_gadget/g1/configs/b.1/f2' action=sys.usb.config=vser && sys.usb.configfs=1 (/vendor/etc/init/hw/init.ums9230_6h10.usb.rc:1118) took 0ms and failed: unlink() failed: No such file or directory
<14>[    7.678990][ T1@C0] init: Command 'rm /config/usb_gadget/g1/configs/b.1/f3' action=sys.usb.config=vser && sys.usb.configfs=1 (/vendor/etc/init/hw/init.ums9230_6h10.usb.rc:1119) took 0ms and failed: unlink() failed: No such file or directory
<14>[    7.681569][ T1@C7] init: Command 'rm /config/usb_gadget/g1/configs/b.1/f4' action=sys.usb.config=vser && sys.usb.configfs=1 (/vendor/etc/init/hw/init.ums9230_6h10.usb.rc:1120) took 2ms and failed: unlink() failed: No such file or directory
<14>[    7.682053][ T1@C7] init: Command 'rm /config/usb_gadget/g1/configs/b.1/f5' action=sys.usb.config=vser && sys.usb.configfs=1 (/vendor/etc/init/hw/init.ums9230_6h10.usb.rc:1121) took 0ms and failed: unlink() failed: No such file or directory
<14>[    7.682522][ T1@C7] init: Command 'rm /config/usb_gadget/g1/configs/b.1/f6' action=sys.usb.config=vser && sys.usb.configfs=1 (/vendor/etc/init/hw/init.ums9230_6h10.usb.rc:1122) took 0ms and failed: unlink() failed: No such file or directory
<14>[    7.683633][ T1@C5] init: Command 'rm /config/usb_gadget/g1/configs/b.1/f7' action=sys.usb.config=vser && sys.usb.configfs=1 (/vendor/etc/init/hw/init.ums9230_6h10.usb.rc:1123) took 1ms and failed: unlink() failed: No such file or directory
<14>[    7.685554][ T1@C4] init: Command 'rm /config/usb_gadget/g1/configs/b.1/f8' action=sys.usb.config=vser && sys.usb.configfs=1 (/vendor/etc/init/hw/init.ums9230_6h10.usb.rc:1124) took 1ms and failed: unlink() failed: No such file or directory
<14>[    7.686409][ T1@C4] init: Command 'rm /config/usb_gadget/g1/configs/b.1/f9' action=sys.usb.config=vser && sys.usb.configfs=1 (/vendor/etc/init/hw/init.ums9230_6h10.usb.rc:1125) took 0ms and failed: unlink() failed: No such file or directory
<14>[    7.686933][ T1@C1] init: Command 'rm /config/usb_gadget/g1/configs/b.1/f10' action=sys.usb.config=vser && sys.usb.configfs=1 (/vendor/etc/init/hw/init.ums9230_6h10.usb.rc:1126) took 0ms and failed: unlink() failed: No such file or directory
<14>[    7.687835][ T1@C2] init: Command 'rm /config/usb_gadget/g1/configs/b.1/f11' action=sys.usb.config=vser && sys.usb.configfs=1 (/vendor/etc/init/hw/init.ums9230_6h10.usb.rc:1127) took 0ms and failed: unlink() failed: No such file or directory
<3>[    7.688743][T216@C2] vser_alloc
<6>[    7.691685][T216@C1] musb-sprd 64900000.usb: musb_sprd_runtime_resume: enter
<6>[    7.691717][T216@C1] musb-sprd 64900000.usb: musb_sprd_resume: enter
<6>[    7.718041][T355@C1] modem modem@1: start over
<6>[    7.718214][T55@C4] WCN BOOT: : BTWF: magic_value=0xf0f0f0a2, wait_count=32
<36>[    7.720209][T304@C2] type=1400 audit(777.123:20): avc:  denied  { dac_override } for  comm="modem_control" capability=1  scontext=u:r:modem_control:s0 tcontext=u:r:modem_control:s0 tclass=capability permissive=0
<36>[    7.721533][T304@C4] type=1400 audit(777.123:21): avc:  denied  { dac_override } for  comm="modem_control" capability=1  scontext=u:r:modem_control:s0 tcontext=u:r:modem_control:s0 tclass=capability permissive=0
<6>[    7.722205][T216@C6] musb-hdrc musb-hdrc.1.auto: musb runtime resume
<6>[    7.722320][T216@C6] musb-sprd 64900000.usb: sprd_musb_enable:DONOTHING
<14>[    7.724143][ T1@C4] init: Service 'vendor.dcxsrv' (pid 451) exited with status 0 oneshot service took 0.065000 seconds in background
<14>[    7.724200][ T1@C4] init: Sending signal 9 to service 'vendor.dcxsrv' (pid 451) process group...
<14>[    7.725724][ T1@C4] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_451
<6>[    7.726429][ T8@C4] musb-sprd 64900000.usb: sm_work: idle state
<6>[    7.726471][ T8@C4] [call_sprd_usbphy_event_notifiers]id(0),val(1)
<6>[    7.726492][ T8@C4] __func__:sprd_hsphy_typec_notifier, event true
<6>[    7.726509][ T8@C4] switch dp/dm to usb
<6>[    7.726747][ T8@C4] musb-sprd 64900000.usb: musb_sprd_otg_start_peripheral: turn on gadget musb-hdrc
<36>[    7.728308][T304@C4] type=1400 audit(777.131:22): avc:  denied  { dac_override } for  comm="modem_control" capability=1  scontext=u:r:modem_control:s0 tcontext=u:r:modem_control:s0 tclass=capability permissive=0
<14>[    7.728719][ T1@C6] init: service 'vendor.cp_diskserver' requested start, but it is already running (flags: 132)
<14>[    7.728749][ T1@C6] init: Control message: Processed ctl.start for 'vendor.cp_diskserver' from pid: 355 (/vendor/bin/modem_control)
<36>[    7.730090][T304@C1] type=1400 audit(777.131:23): avc:  denied  { dac_override } for  comm="modem_control" capability=1  scontext=u:r:modem_control:s0 tcontext=u:r:modem_control:s0 tclass=capability permissive=0
<36>[    7.730850][T304@C4] type=1400 audit(777.135:24): avc:  denied  { dac_override } for  comm="modem_control" capability=1  scontext=u:r:modem_control:s0 tcontext=u:r:modem_control:s0 tclass=capability permissive=0
<14>[    7.732545][ T1@C6] init: processing action (vendor.modem.sp-boot=1) from (/vendor/etc/init/init.md.rc:479)
<14>[    7.737914][ T1@C5] init: processing action (sys.usb.state=vser && vendor.ro.bootmode=cali) from (/vendor/etc/init/engpc.rc:13)
<6>[    7.738030][T55@C4] WCN BOOT: : BTWF: magic_value=0xf0f0f0a3, wait_count=34
<6>[    7.886125][T386@C4] musb-hdrc musb-hdrc.1.auto: gadget D+ pullup on
<6>[    7.886249][ T8@C4] musb-sprd 64900000.usb: sm_work: peripheral state
<6>[    7.943934][T55@C4] WCN BOOT: : BTWF: magic_value=0xf0f0f0a5, wait_count=54
<6>[    7.954026][T55@C4] WCN BOOT: : BTWF: magic_value=0xf0f0f0a6, wait_count=55
<6>[    7.975720][T313@C7] sprd-sbuf: channel 3-4, state=1, recv open msg!
<6>[    7.975743][T316@C6] sprd-sbuf: channel 3-5, state=1, recv open msg!
<6>[    7.975760][T316@C6] sprd-sbuf: smsg_open_ack: channel 3-5!
<6>[    7.975790][T313@C7] sprd-sbuf: smsg_open_ack: channel 3-4!
<6>[    7.976295][T313@C7] sprd-sbuf: channel 3-4 state = 0, recv cmd msg, flag = 1!
<6>[    7.976331][T313@C7] WCN BASE: sbuf index 1 dts[3] chn[4] read event[0]
<6>[    7.976341][T313@C7] WCN BASE: wcn_sipc_chn_set_status chn: 4 ,  flag:1
<6>[    7.976350][T313@C7] WCN BASE: sbuf index 9 dts[3] chn[4] read event[0]
<6>[    7.976359][T313@C7] WCN BASE: sbuf index 13 dts[3] chn[4] read event[0]
<6>[    7.977319][T314@C7] sprd-sbuf: channel 3-11, state=1, recv open msg!
<6>[    7.977339][T314@C7] sprd-sbuf: smsg_open_ack: channel 3-11!
<6>[    7.977385][T316@C6] sprd-sbuf: channel 3-5 state = 0, recv cmd msg, flag = 1!
<6>[    7.977421][T316@C6] WCN BASE: sbuf index 4 dts[3] chn[5] read event[0]
<6>[    7.977485][T314@C7] sprd-sbuf: channel 3-11 state = 0, recv cmd msg, flag = 1!
<6>[    7.977515][T314@C7] WCN BASE: sbuf index 2 dts[3] chn[11] read event[0]
<6>[    7.977933][T315@C7] sprd-sbuf: channel 3-12, state=1, recv open msg!
<6>[    7.978010][T315@C7] sprd-sbuf: smsg_open_ack: channel 3-12!
<6>[    7.978129][T315@C7] sprd-sbuf: channel 3-12 state = 0, recv cmd msg, flag = 1!
<6>[    7.978158][T315@C7] WCN BASE: sbuf index 3 dts[3] chn[12] read event[0]
<6>[    7.980489][    C0] unisoc-mailbox smsg channel 7 not opened! drop smsg: type=1, flag=0xbeee, value=0x00000000
<6>[    7.985074][T55@C4] WCN BOOT: : BTWF: marlin cp init ready!!!
<6>[    7.985205][T89@C4] WCN BASE: cp2 power status:1
<6>[    7.985228][T89@C4] WCN BASE: start_loopcheck
<6>[    7.985264][T89@C4] WCN BASE: s_marlin_bootup_time=7985264007
<6>[    7.985291][T89@C4] WCN BOOT: : after start2 malrin status[4] BT:0 FM:0 WIFI:4 MDBG:0
<6>[    7.985312][T89@C4] WCN BOOT: : after start2 gnss status[0] GPS:0 GNSS_BD:0 GNSS_GAL:0
<6>[    7.985335][T89@C4] sprd-wlan: sipc_post_init: register 6 ops
<6>[    7.985357][T89@C4] WCN BASE: [wcn_sipc_chn_init]:index[17] chn[7]
<6>[    7.985671][T55@C4] WCN BASE: mdbg_tx_cb, chn:0
<6>[    7.986226][T313@C7] WCN BASE: : AT cmd read:Platform Version:WCN_TRUNK_22A_W24.38.3Project Version:uww2631_qogirL6HW Version:....09-18-2024 16:26:32
<6>[    7.986253][T89@C4] WCN BASE: sblock chn[17] create success!
<6>[    7.986273][T89@C4] WCN BUS: [+]bus_chn_init(17, 2)
<6>[    7.986275][T481@C6] unisoc-mailbox channel 3-7 send open msg!
<6>[    7.986294][T89@C4] WCN BUS: [-]bus_chn_init(17)
<6>[    7.986308][T481@C6] unisoc-mailbox channel 3-7 success
<6>[    7.986311][T89@C4] WCN BASE: sipc chn[17] init success!
<6>[    7.986329][T89@C4] WCN BASE: [wcn_sipc_chn_init]:index[19] chn[8]
<6>[    7.986347][T89@C4] WCN BASE: sipc chn[19] clear debug point!
<6>[    7.987173][T55@C4] WCN BOOT: : wcn_set_armlog_status:at+armlog=1
<6>[    7.987292][T55@C4] WCN BASE: mdbg_tx_cb, chn:0
<6>[    7.987883][T313@C7] WCN BASE: : AT cmd read:OK
<6>[    7.988367][T55@C4] WCN BASE: mdbg_tx_cb, chn:0
<6>[    7.988542][T89@C4] WCN BASE: sblock chn[19] create success!
<6>[    7.988561][T89@C4] WCN BUS: [+]bus_chn_init(19, 2)
<6>[    7.988569][T482@C7] unisoc-mailbox channel 3-8 send open msg!
<6>[    7.988582][T89@C4] WCN BUS: [-]bus_chn_init(19)
<6>[    7.988599][T89@C4] WCN BASE: sipc chn[19] init success!
<6>[    7.988617][T89@C4] WCN BASE: [wcn_sipc_chn_init]:index[21] chn[9]
<6>[    7.988885][T313@C7] WCN BASE: : AT cmd read:+LOGLEVEL: 3
<6>[    7.989749][T89@C4] WCN BASE: sblock chn[21] create success!
<6>[    7.989772][T89@C4] WCN BUS: [+]bus_chn_init(21, 2)
<6>[    7.989776][T483@C6] unisoc-mailbox channel 3-9 send open msg!
<6>[    7.989792][T89@C4] WCN BUS: [-]bus_chn_init(21)
<6>[    7.989809][T89@C4] WCN BASE: sipc chn[21] init success!
<6>[    7.989829][T89@C4] WCN BASE: [wcn_sipc_chn_init]:index[16] chn[7]
<6>[    7.990828][T89@C4] WCN BASE: sblock chn[16] create success!
<6>[    7.990854][T89@C4] WCN BUS: [+]bus_chn_init(16, 2)
<6>[    7.990876][T89@C4] WCN BUS: [-]bus_chn_init(16)
<6>[    7.990893][T89@C4] WCN BASE: sipc chn[16] init success!
<6>[    7.990912][T89@C4] WCN BASE: [wcn_sipc_chn_init]:index[18] chn[8]
<6>[    7.990932][T89@C4] WCN BASE: sipc chn[18] clear debug point!
<6>[    7.990951][T485@C3] WCN BASE: channel 3-7(16), chn_deinit?
<6>[    7.990973][T485@C3] WCN BASE: [wcn_sipc_sblk_push_list_dequeue] 647 chn[16]
<6>[    7.991832][T89@C4] WCN BASE: sblock chn[18] create success!
<6>[    7.991857][T89@C4] WCN BUS: [+]bus_chn_init(18, 1)
<6>[    7.991878][T89@C4] WCN BUS: [-]bus_chn_init(18)
<6>[    7.991890][T486@C3] WCN BASE: channel 3-8(18), chn_deinit?
<6>[    7.991895][T89@C4] WCN BASE: sipc chn[18] init success!
<6>[    7.991914][T89@C4] WCN BASE: [wcn_sipc_chn_init]:index[20] chn[9]
<6>[    7.992356][T89@C4] WCN BASE: sblock chn[20] create success!
<6>[    7.992375][T89@C4] WCN BUS: [+]bus_chn_init(20, 1)
<6>[    7.992395][T89@C4] WCN BUS: [-]bus_chn_init(20)
<6>[    7.992411][T89@C4] WCN BASE: sipc chn[20] init success!
<6>[    7.992410][T487@C1] WCN BASE: channel 3-9(20), chn_deinit?
<4>[    7.992459][T89@C4] sprd-wlan: [7978]cid 0 tx[CMD_SYNC_VERSION]
<6>[    7.992805][T417@C0] WCN BASE: [wcn_sipc_buf_list_alloc] 298 chn[16]
<3>[    7.992847][T417@C0] WCN BASE error: chn status(0)! sipc_chn(7)
<3>[    7.992864][T417@C0] WCN BASE error: sipc chn 7 not created!
<6>[    7.992881][T417@C0] WCN BASE: [wcn_sipc_buf_list_free] 312 chn[16]
<3>[    7.992911][T417@C0] sprd-wlan: tx_cmd, 226. tx cmd err : -4 firstly
<6>[    8.037636][T481@C6] sprd-sblock: sblock thread recv msg: dst=3, channel=7, type=5, flag=0x0001, value=0x00000000
<6>[    8.037685][T481@C6] sprd-sblock: send init done!
<6>[    8.037693][T481@C6] sprd-sblock: channel 3-7, SMSG_CMD_SBLOCK_INIT, dst address = 0x2c4000!
<6>[    8.037704][T481@C6] WCN BASE: wcn_sipc_sblk_notifer  785 index:17  event:8
<6>[    8.037715][T481@C6] WCN BASE: wcn_sipc_chn_set_status chn: 7 ,  flag:1
<6>[    8.050053][T417@C0] WCN BASE: [wcn_sipc_buf_list_alloc] 298 chn[16]
<6>[    8.050114][T417@C0] WCN BASE: [wcn_sipc_push_list_enqueue] 398 chn[16]
<6>[    8.050133][T417@C0] WCN BASE: [wcn_sipc_wakeup_tx] 326 chn[16]
<6>[    8.050179][T417@C0] WCN BASE: [wcn_sipc_sblk_push] 720 chn[16]
<6>[    8.050290][T485@C6] WCN BASE: [wcn_sipc_work_func] 899 chn[16] during compelte pt:59340(us)
<6>[    8.050349][T485@C6] WCN BASE: [wcn_sipc_sblk_send] 626 chn[16]
<6>[    8.050358][T485@C6] WCN BASE: [wcn_sipc_sblk_push_list_dequeue] 672 chn[16]
<6>[    8.050370][T485@C6] WCN BASE: [wcn_sipc_pop_list_enqueue] 346 chn[16]
<6>[    8.050378][T485@C6] WCN BASE: [wcn_sipc_pop_list_flush] 358 chn[16]
<6>[    8.050396][T485@C6] sprd-wlan: tx_cmd_pop num: 1,cmd_poped=1, cmd_send=1
<6>[    8.050405][T485@C6] WCN BASE: [wcn_sipc_buf_list_free] 312 chn[16]
<6>[    8.050422][T485@C6] WCN BASE: [wcn_sipc_sblk_push_list_dequeue] 687 chn[16]
<6>[    8.060249][T481@C6] WCN BASE: wcn_sipc_sblk_notifer  785 index:17  event:2
<6>[    8.060928][T89@C4] sprd-wlan: wifi_nvm_parse...
<6>[    8.067324][T89@C4] sprd-wlan: wifi_nvm_parse read wifi_board_config.ini data_len:0x1d1c
<6>[    8.067363][T89@C4] sprd-wlan:  Version 
<6>[    8.067393][T89@C4] sprd-wlan: [g_table]Major, offset:0, num:1, value:			3 0 0 0 0 0 0 0 0 0 
<6>[    8.067418][T89@C4] sprd-wlan: [g_table]Minor, offset:2, num:1, value:			0 0 0 0 0 0 0 0 0 0 
<6>[    8.067440][T89@C4] sprd-wlan:  Board 
<6>[    8.067456][T89@C4] sprd-wlan:  Config 
<6>[    8.067475][T89@C4] sprd-wlan: [g_table]Calibration_Bypass, offset:4, num:1, value:			3566 0 0 0 0 0 0 0 0 0 
<6>[    8.067498][T89@C4] sprd-wlan: [g_table]2G_Chain_Mask, offset:6, num:1, value:			2 0 0 0 0 0 0 0 0 0 
<6>[    8.067519][T89@C4] sprd-wlan: [g_table]5G_Chain_Mask, offset:7, num:1, value:			2 0 0 0 0 0 0 0 0 0 
<6>[    8.067540][T89@C4] sprd-wlan:  Board 
<6>[    8.067555][T89@C4] sprd-wlan:  Config 
<6>[    8.067571][T89@C4] sprd-wlan:  TPC 
<6>[    8.067591][T89@C4] sprd-wlan: [g_table]DPD_LUT_idx, offset:8, num:8, value:			51 51 0 17 34 51 51 51 0 0 
<6>[    8.067615][T89@C4] sprd-wlan: [g_table]TPC_Goal_Chain0, offset:16, num:8, value:			166 127 126 134 166 127 126 134 0 0 
<6>[    8.067638][T89@C4] sprd-wlan: [g_table]TPC_Goal_Chain1, offset:32, num:8, value:			181 168 165 148 181 168 165 148 0 0 
<6>[    8.067661][T89@C4] sprd-wlan:  TPC-LUT 
<6>[    8.067679][T89@C4] sprd-wlan: [g_table]2G_LUT_0, offset:48, num:4, value:			6 0 40 0 0 0 0 0 0 0 
<6>[    8.067701][T89@C4] sprd-wlan: [g_table]2G_LUT_1, offset:52, num:4, value:			6 0 24 0 0 0 0 0 0 0 
<6>[    8.067723][T89@C4] sprd-wlan: [g_table]2G_LUT_2, offset:56, num:4, value:			6 0 8 0 0 0 0 0 0 0 
<6>[    8.067744][T89@C4] sprd-wlan: [g_table]2G_LUT_3, offset:60, num:4, value:			10 0 8 0 0 0 0 0 0 0 
<6>[    8.067766][T89@C4] sprd-wlan: [g_table]2G_LUT_4, offset:64, num:4, value:			14 0 8 0 0 0 0 0 0 0 
<6>[    8.067787][T89@C4] sprd-wlan: [g_table]2G_LUT_5, offset:68, num:4, value:			18 0 8 0 0 0 0 0 0 0 
<6>[    8.067809][T89@C4] sprd-wlan: [g_table]2G_LUT_6, offset:72, num:4, value:			22 0 8 0 0 0 0 0 0 0 
<6>[    8.067830][T89@C4] sprd-wlan: [g_table]2G_LUT_7, offset:76, num:4, value:			26 0 8 0 0 0 0 0 0 0 
<6>[    8.067852][T89@C4] sprd-wlan: [g_table]5G_LUT_0, offset:80, num:4, value:			6 0 24 0 0 0 0 0 0 0 
<6>[    8.067874][T89@C4] sprd-wlan: [g_table]5G_LUT_1, offset:84, num:4, value:			6 0 8 0 0 0 0 0 0 0 
<6>[    8.067896][T89@C4] sprd-wlan: [g_table]5G_LUT_2, offset:88, num:4, value:			10 0 8 0 0 0 0 0 0 0 
<6>[    8.067918][T89@C4] sprd-wlan: [g_table]5G_LUT_3, offset:92, num:4, value:			14 0 8 0 0 0 0 0 0 0 
<6>[    8.067940][T89@C4] sprd-wlan: [g_table]5G_LUT_4, offset:96, num:4, value:			18 0 8 0 0 0 0 0 0 0 
<6>[    8.067962][T89@C4] sprd-wlan: [g_table]5G_LUT_5, offset:100, num:4, value:			22 0 8 0 0 0 0 0 0 0 
<6>[    8.067984][T89@C4] sprd-wlan: [g_table]5G_LUT_6, offset:104, num:4, value:			26 0 8 0 0 0 0 0 0 0 
<6>[    8.068006][T89@C4] sprd-wlan: [g_table]5G_LUT_7, offset:108, num:4, value:			30 0 8 0 0 0 0 0 0 0 
<6>[    8.068028][T89@C4] sprd-wlan:  Board 
<6>[    8.068044][T89@C4] sprd-wlan:  Config 
<6>[    8.068060][T89@C4] sprd-wlan:  Frequency 
<6>[    8.068076][T89@C4] sprd-wlan:  Compensation 
<6>[    8.068097][T89@C4] sprd-wlan: [g_table]2G_Channel_Chain0, offset:112, num:14, value:			2 2 2 2 1 1 1 1 1 1 
<6>[    8.068120][T89@C4] sprd-wlan: [g_table]2G_Channel_Chain1, offset:126, num:14, value:			2 2 2 2 1 1 1 1 1 1 
<6>[    8.068144][T89@C4] sprd-wlan: [g_table]5G_Channel_Chain0, offset:140, num:25, value:			3 2 2 1 2 1 1 1 3 2 
<6>[    8.068169][T89@C4] sprd-wlan: [g_table]5G_Channel_Chain1, offset:165, num:25, value:			3 2 2 1 2 1 1 1 3 2 
<6>[    8.068190][T89@C4] sprd-wlan:  Rate 
<6>[    8.068206][T89@C4] sprd-wlan:  To 
<6>[    8.068222][T89@C4] sprd-wlan:  Power 
<6>[    8.068238][T89@C4] sprd-wlan:  with 
<6>[    8.068254][T89@C4] sprd-wlan:  BW 
<6>[    8.068270][T89@C4] sprd-wlan:  20M 
<6>[    8.068289][T89@C4] sprd-wlan: [g_table]11b_Power, offset:192, num:4, value:			34 34 34 34 0 0 0 0 0 0 
<6>[    8.068312][T89@C4] sprd-wlan: [g_table]11g_Power, offset:196, num:8, value:			56 56 56 56 60 60 64 64 0 0 
<6>[    8.068335][T89@C4] sprd-wlan: [g_table]11a_Power, offset:204, num:8, value:			42 42 48 48 52 52 56 56 0 0 
<6>[    8.068359][T89@C4] sprd-wlan: [g_table]11n_2G_Power, offset:212, num:17, value:			60 60 60 68 68 72 72 72 60 60 
<6>[    8.068383][T89@C4] sprd-wlan: [g_table]11n_5G_Power, offset:229, num:17, value:			48 48 48 56 56 60 60 60 48 48 
<6>[    8.068407][T89@C4] sprd-wlan: [g_table]11ac_Power, offset:246, num:20, value:			48 48 48 52 52 56 56 56 60 60 
<6>[    8.068429][T89@C4] sprd-wlan:  Power 
<6>[    8.068444][T89@C4] sprd-wlan:  Backoff 
<6>[    8.068463][T89@C4] sprd-wlan: [g_table]Green_WIFI_offset, offset:269, num:1, value:			0 0 0 0 0 0 0 0 0 0 
<6>[    8.068485][T89@C4] sprd-wlan: [g_table]HT40_2G_Power_offset, offset:270, num:1, value:			8 0 0 0 0 0 0 0 0 0 
<6>[    8.068507][T89@C4] sprd-wlan: [g_table]HT40_5G_Power_offset, offset:271, num:1, value:			0 0 0 0 0 0 0 0 0 0 
<6>[    8.068529][T89@C4] sprd-wlan: [g_table]VHT40_Power_offset, offset:272, num:1, value:			0 0 0 0 0 0 0 0 0 0 
<6>[    8.068551][T89@C4] sprd-wlan: [g_table]VHT80_Power_offset, offset:273, num:1, value:			0 0 0 0 0 0 0 0 0 0 
<6>[    8.068574][T89@C4] sprd-wlan: [g_table]SAR_Power_offset, offset:274, num:1, value:			0 0 0 0 0 0 0 0 0 0 
<6>[    8.068596][T89@C4] sprd-wlan: [g_table]Mean_Power_offset, offset:275, num:1, value:			36 0 0 0 0 0 0 0 0 0 
<6>[    8.068618][T89@C4] sprd-wlan: [g_table]APC_mode, offset:276, num:1, value:			1 0 0 0 0 0 0 0 0 0 
<6>[    8.068640][T89@C4] sprd-wlan: [g_table]MAGIC_word, offset:277, num:1, value:			170 0 0 0 0 0 0 0 0 0 
<6>[    8.068661][T89@C4] sprd-wlan:  Reg 
<6>[    8.068677][T89@C4] sprd-wlan:  Domain 
<6>[    8.068696][T89@C4] sprd-wlan: [g_table]reg_domain1, offset:280, num:1, value:			1 0 0 0 0 0 0 0 0 0 
<6>[    8.068718][T89@C4] sprd-wlan: [g_table]reg_domain2, offset:284, num:1, value:			2 0 0 0 0 0 0 0 0 0 
<6>[    8.068739][T89@C4] sprd-wlan:  Band 
<6>[    8.068755][T89@C4] sprd-wlan:  Edge 
<6>[    8.068770][T89@C4] sprd-wlan:  Power 
<6>[    8.068786][T89@C4] sprd-wlan:  offset 
<6>[    8.068802][T89@C4] sprd-wlan:  MKK 
<6>[    8.068817][T89@C4] sprd-wlan:  FCC 
<6>[    8.068833][T89@C4] sprd-wlan:  ETSI 
<6>[    8.068855][T89@C4] sprd-wlan: [g_table]BW20M, offset:288, num:39, value:			0 0 0 0 0 0 0 0 0 0 
<6>[    8.068880][T89@C4] sprd-wlan: [g_table]BW40M, offset:327, num:21, value:			0 0 0 0 0 0 0 0 0 0 
<6>[    8.068902][T89@C4] sprd-wlan: [g_table]BW80M, offset:348, num:6, value:			0 0 0 0 0 0 0 0 0 0 
<6>[    8.068927][T89@C4] sprd-wlan:  TX 
<6>[    8.068943][T89@C4] sprd-wlan:  Scale 
<6>[    8.068964][T89@C4] sprd-wlan: [g_table]Chain0_1, offset:356, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.068988][T89@C4] sprd-wlan: [g_table]Chain1_1, offset:980, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069011][T89@C4] sprd-wlan: [g_table]Chain0_2, offset:372, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069034][T89@C4] sprd-wlan: [g_table]Chain1_2, offset:996, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069058][T89@C4] sprd-wlan: [g_table]Chain0_3, offset:388, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069082][T89@C4] sprd-wlan: [g_table]Chain1_3, offset:1012, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069106][T89@C4] sprd-wlan: [g_table]Chain0_4, offset:404, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069130][T89@C4] sprd-wlan: [g_table]Chain1_4, offset:1028, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069153][T89@C4] sprd-wlan: [g_table]Chain0_5, offset:420, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069177][T89@C4] sprd-wlan: [g_table]Chain1_5, offset:1044, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069200][T89@C4] sprd-wlan: [g_table]Chain0_6, offset:436, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069224][T89@C4] sprd-wlan: [g_table]Chain1_6, offset:1060, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069248][T89@C4] sprd-wlan: [g_table]Chain0_7, offset:452, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069272][T89@C4] sprd-wlan: [g_table]Chain1_7, offset:1076, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069295][T89@C4] sprd-wlan: [g_table]Chain0_8, offset:468, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069319][T89@C4] sprd-wlan: [g_table]Chain1_8, offset:1092, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069343][T89@C4] sprd-wlan: [g_table]Chain0_9, offset:484, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069367][T89@C4] sprd-wlan: [g_table]Chain1_9, offset:1108, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069391][T89@C4] sprd-wlan: [g_table]Chain0_10, offset:500, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069415][T89@C4] sprd-wlan: [g_table]Chain1_10, offset:1124, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069438][T89@C4] sprd-wlan: [g_table]Chain0_11, offset:516, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069462][T89@C4] sprd-wlan: [g_table]Chain1_11, offset:1140, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069486][T89@C4] sprd-wlan: [g_table]Chain0_12, offset:532, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069510][T89@C4] sprd-wlan: [g_table]Chain1_12, offset:1156, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069534][T89@C4] sprd-wlan: [g_table]Chain0_13, offset:548, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069559][T89@C4] sprd-wlan: [g_table]Chain1_13, offset:1172, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069583][T89@C4] sprd-wlan: [g_table]Chain0_14, offset:564, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069607][T89@C4] sprd-wlan: [g_table]Chain1_14, offset:1188, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069631][T89@C4] sprd-wlan: [g_table]Chain0_36, offset:580, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069656][T89@C4] sprd-wlan: [g_table]Chain1_36, offset:1204, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069680][T89@C4] sprd-wlan: [g_table]Chain0_40, offset:596, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069704][T89@C4] sprd-wlan: [g_table]Chain1_40, offset:1220, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069729][T89@C4] sprd-wlan: [g_table]Chain0_44, offset:612, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069753][T89@C4] sprd-wlan: [g_table]Chain1_44, offset:1236, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069778][T89@C4] sprd-wlan: [g_table]Chain0_48, offset:628, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069803][T89@C4] sprd-wlan: [g_table]Chain1_48, offset:1252, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069828][T89@C4] sprd-wlan: [g_table]Chain0_52, offset:644, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069853][T89@C4] sprd-wlan: [g_table]Chain1_52, offset:1268, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069878][T89@C4] sprd-wlan: [g_table]Chain0_56, offset:660, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069903][T89@C4] sprd-wlan: [g_table]Chain1_56, offset:1284, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.069927][T89@C4] sprd-wlan: [g_table]Chain0_60, offset:676, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070047][T89@C4] sprd-wlan: [g_table]Chain1_60, offset:1300, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070075][T89@C4] sprd-wlan: [g_table]Chain0_64, offset:692, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070101][T89@C4] sprd-wlan: [g_table]Chain1_64, offset:1316, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070127][T89@C4] sprd-wlan: [g_table]Chain0_100, offset:708, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070152][T89@C4] sprd-wlan: [g_table]Chain1_100, offset:1332, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070177][T89@C4] sprd-wlan: [g_table]Chain0_104, offset:724, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070203][T89@C4] sprd-wlan: [g_table]Chain1_104, offset:1348, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070228][T89@C4] sprd-wlan: [g_table]Chain0_108, offset:740, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070254][T89@C4] sprd-wlan: [g_table]Chain1_108, offset:1364, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070280][T89@C4] sprd-wlan: [g_table]Chain0_112, offset:756, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070305][T89@C4] sprd-wlan: [g_table]Chain1_112, offset:1380, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070331][T89@C4] sprd-wlan: [g_table]Chain0_116, offset:772, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070356][T89@C4] sprd-wlan: [g_table]Chain1_116, offset:1396, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070381][T89@C4] sprd-wlan: [g_table]Chain0_120, offset:788, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070406][T89@C4] sprd-wlan: [g_table]Chain1_120, offset:1412, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070432][T89@C4] sprd-wlan: [g_table]Chain0_124, offset:804, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070457][T89@C4] sprd-wlan: [g_table]Chain1_124, offset:1428, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070482][T89@C4] sprd-wlan: [g_table]Chain0_128, offset:820, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070508][T89@C4] sprd-wlan: [g_table]Chain1_128, offset:1444, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070533][T89@C4] sprd-wlan: [g_table]Chain0_132, offset:836, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070558][T89@C4] sprd-wlan: [g_table]Chain1_132, offset:1460, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070587][T89@C4] sprd-wlan: [g_table]Chain0_136, offset:852, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070613][T89@C4] sprd-wlan: [g_table]Chain1_136, offset:1476, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070639][T89@C4] sprd-wlan: [g_table]Chain0_140, offset:868, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070665][T89@C4] sprd-wlan: [g_table]Chain1_140, offset:1492, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070691][T89@C4] sprd-wlan: [g_table]Chain0_144, offset:884, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070717][T89@C4] sprd-wlan: [g_table]Chain1_144, offset:1508, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070743][T89@C4] sprd-wlan: [g_table]Chain0_149, offset:900, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070769][T89@C4] sprd-wlan: [g_table]Chain1_149, offset:1524, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070795][T89@C4] sprd-wlan: [g_table]Chain0_153, offset:916, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070820][T89@C4] sprd-wlan: [g_table]Chain1_153, offset:1540, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070847][T89@C4] sprd-wlan: [g_table]Chain0_157, offset:932, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070873][T89@C4] sprd-wlan: [g_table]Chain1_157, offset:1556, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070899][T89@C4] sprd-wlan: [g_table]Chain0_161, offset:948, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070926][T89@C4] sprd-wlan: [g_table]Chain1_161, offset:1572, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070952][T89@C4] sprd-wlan: [g_table]Chain0_165, offset:964, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070978][T89@C4] sprd-wlan: [g_table]Chain1_165, offset:1588, num:16, value:			1 2 3 4 5 6 7 8 9 10 
<6>[    8.070999][T89@C4] sprd-wlan:  misc 
<6>[    8.071019][T89@C4] sprd-wlan: [g_table]DFS_switch, offset:1604, num:1, value:			1 0 0 0 0 0 0 0 0 0 
<6>[    8.071042][T89@C4] sprd-wlan: [g_table]power_save_switch, offset:1605, num:1, value:			2 0 0 0 0 0 0 0 0 0 
<6>[    8.071065][T89@C4] sprd-wlan: [g_table]rssi_report_diff, offset:1606, num:1, value:			4 0 0 0 0 0 0 0 0 0 
<6>[    8.071087][T89@C4] sprd-wlan: [g_table]Ex_FEM_En, offset:1607, num:1, value:			0 0 0 0 0 0 0 0 0 0 
<6>[    8.071110][T89@C4] sprd-wlan: [g_table]TX_En_Ctrl, offset:1608, num:1, value:			1 0 0 0 0 0 0 0 0 0 
<6>[    8.071132][T89@C4] sprd-wlan: [g_table]LNA_En_Ctrl, offset:1609, num:1, value:			2 0 0 0 0 0 0 0 0 0 
<6>[    8.071154][T89@C4] sprd-wlan: [g_table]SW_En_Ctrl, offset:1610, num:1, value:			4 0 0 0 0 0 0 0 0 0 
<6>[    8.071176][T89@C4] sprd-wlan: [g_table]LNA_Gain, offset:1611, num:1, value:			5 0 0 0 0 0 0 0 0 0 
<6>[    8.071199][T89@C4] sprd-wlan: [g_table]LNA_Bypass_Gain, offset:1612, num:1, value:			-5 0 0 0 0 0 0 0 0 0 
<6>[    8.071222][T89@C4] sprd-wlan: [g_table]Ex_FEM_PDET, offset:1613, num:1, value:			6 0 0 0 0 0 0 0 0 0 
<6>[    8.071243][T89@C4] sprd-wlan:  debug 
<6>[    8.071258][T89@C4] sprd-wlan:  reg 
<6>[    8.071280][T89@C4] sprd-wlan: [g_table]address, offset:1616, num:16, value:			1 2 3 4 5 6 7 8 9 16 
<6>[    8.071306][T89@C4] sprd-wlan: [g_table]value, offset:1680, num:16, value:			1 25 24 25 32 33 34 35 36 37 
<6>[    8.071330][T89@C4] sprd-wlan: wifi_nvm_buf_operate special_data_flag: 32
<6>[    8.071347][T89@C4] sprd-wlan:  coex_config 
<6>[    8.071367][T89@C4] sprd-wlan: [g_table]bt_performance_cfg0, offset:1744, num:1, value:			16843009 0 0 0 0 0 0 0 0 0 
<6>[    8.071390][T89@C4] sprd-wlan: [g_table]bt_performance_cfg1, offset:1748, num:1, value:			16777216 0 0 0 0 0 0 0 0 0 
<6>[    8.071414][T89@C4] sprd-wlan: [g_table]wifi_performance_cfg0, offset:1752, num:1, value:			17107457 0 0 0 0 0 0 0 0 0 
<6>[    8.071437][T89@C4] sprd-wlan: [g_table]wifi_performance_cfg2, offset:1756, num:1, value:			0 0 0 0 0 0 0 0 0 0 
<6>[    8.071461][T89@C4] sprd-wlan: [g_table]strategy_cfg0, offset:1760, num:1, value:			16843008 0 0 0 0 0 0 0 0 0 
<6>[    8.071484][T89@C4] sprd-wlan: [g_table]strategy_cfg1, offset:1764, num:1, value:			50331648 0 0 0 0 0 0 0 0 0 
<6>[    8.071507][T89@C4] sprd-wlan: [g_table]strategy_cfg2, offset:1768, num:1, value:			134348800 0 0 0 0 0 0 0 0 0 
<6>[    8.071531][T89@C4] sprd-wlan: [g_table]compatibility_cfg0, offset:1772, num:1, value:			67371008 0 0 0 0 0 0 0 0 0 
<6>[    8.071555][T89@C4] sprd-wlan: [g_table]compatibility_cfg1, offset:1776, num:1, value:			0 0 0 0 0 0 0 0 0 0 
<6>[    8.071577][T89@C4] sprd-wlan: [g_table]ant_cfg0, offset:1780, num:1, value:			3 0 0 0 0 0 0 0 0 0 
<6>[    8.071600][T89@C4] sprd-wlan: [g_table]ant_cfg1, offset:1784, num:1, value:			0 0 0 0 0 0 0 0 0 0 
<6>[    8.071622][T89@C4] sprd-wlan: [g_table]isolation_cfg0, offset:1788, num:1, value:			16 0 0 0 0 0 0 0 0 0 
<6>[    8.071645][T89@C4] sprd-wlan: [g_table]isolation_cfg1, offset:1792, num:1, value:			0 0 0 0 0 0 0 0 0 0 
<6>[    8.071668][T89@C4] sprd-wlan: [g_table]reserved_cfg0, offset:1796, num:1, value:			0 0 0 0 0 0 0 0 0 0 
<6>[    8.071691][T89@C4] sprd-wlan: [g_table]reserved_cfg1, offset:1800, num:1, value:			0 0 0 0 0 0 0 0 0 0 
<6>[    8.071714][T89@C4] sprd-wlan: [g_table]reserved_cfg2, offset:1804, num:1, value:			0 0 0 0 0 0 0 0 0 0 
<6>[    8.071737][T89@C4] sprd-wlan: [g_table]reserved_cfg3, offset:1808, num:1, value:			0 0 0 0 0 0 0 0 0 0 
<6>[    8.071760][T89@C4] sprd-wlan: [g_table]reserved_cfg4, offset:1812, num:1, value:			0 0 0 0 0 0 0 0 0 0 
<6>[    8.071782][T89@C4] sprd-wlan: [g_table]reserved_cfg5, offset:1816, num:1, value:			0 0 0 0 0 0 0 0 0 0 
<6>[    8.071805][T89@C4] sprd-wlan: [g_table]reserved_cfg6, offset:1820, num:1, value:			0 0 0 0 0 0 0 0 0 0 
<6>[    8.071828][T89@C4] sprd-wlan: [g_table]reserved_cfg7, offset:1824, num:1, value:			0 0 0 0 0 0 0 0 0 0 
<6>[    8.071849][T89@C4] sprd-wlan:  rf_tlv_config 
<6>[    8.071877][T89@C4] sprd-wlan: [g_table]rf_config, offset:1832, num:45, value:			170 85 0 255 11 11 0 80 0 0 
<6>[    8.071924][T89@C4] sprd-wlan:  wifi_config_param 
<6>[    8.071944][T89@C4] sprd-wlan: [g_table]roaming_trigger, offset:3332, num:1, value:			70 0 0 0 0 0 0 0 0 0 
<6>[    8.071967][T89@C4] sprd-wlan: [g_table]roaming_delta, offset:3333, num:1, value:			5 0 0 0 0 0 0 0 0 0 
<6>[    8.071990][T89@C4] sprd-wlan: [g_table]roaming_5g_prefer, offset:3334, num:1, value:			5 0 0 0 0 0 0 0 0 0 
<6>[    8.072011][T89@C4] sprd-wlan:  ap_oui_config 
<6>[    8.072033][T89@C4] sprd-wlan: [g_table]oui_config, offset:3340, num:5, value:			895 2795 3139 3303 5278053 0 0 0 0 0 
<6>[    8.072055][T89@C4] sprd-wlan:  ap 
<6>[    8.072070][T89@C4] sprd-wlan:  config 
<6>[    8.072091][T89@C4] sprd-wlan: [g_table]ap_config, offset:3544, num:8, value:			224 4 0 1 168 21 77 255 0 0 
<6>[    8.072149][T89@C4] sprd-wlan: wifi_nvm_parse(), parsing ini data result=0
<6>[    8.072166][T89@C4] sprd-wlan: download the first section of config file
<4>[    8.072214][T89@C4] sprd-wlan: [8058]cid 0 tx[CMD_DOWNLOAD_INI]
<6>[    8.072333][T417@C0] WCN BASE: [wcn_sipc_buf_list_alloc] 298 chn[16]
<6>[    8.072379][T417@C0] WCN BASE: [wcn_sipc_push_list_enqueue] 398 chn[16]
<6>[    8.072397][T417@C0] WCN BASE: [wcn_sipc_wakeup_tx] 326 chn[16]
<6>[    8.072454][T417@C0] WCN BASE: [wcn_sipc_sblk_push] 720 chn[16]
<6>[    8.072474][T485@C6] WCN BASE: [wcn_sipc_work_func] 899 chn[16] during compelte pt:22169(us)
<6>[    8.072548][T485@C6] WCN BASE: [wcn_sipc_sblk_send] 626 chn[16]
<6>[    8.072557][T485@C6] WCN BASE: [wcn_sipc_sblk_push_list_dequeue] 672 chn[16]
<6>[    8.072569][T485@C6] WCN BASE: [wcn_sipc_pop_list_enqueue] 346 chn[16]
<6>[    8.072578][T485@C6] WCN BASE: [wcn_sipc_pop_list_flush] 358 chn[16]
<6>[    8.072594][T485@C6] sprd-wlan: tx_cmd_pop num: 1,cmd_poped=2, cmd_send=2
<6>[    8.072604][T485@C6] WCN BASE: [wcn_sipc_buf_list_free] 312 chn[16]
<6>[    8.072622][T485@C6] WCN BASE: [wcn_sipc_sblk_push_list_dequeue] 687 chn[16]
<6>[    8.081316][T481@C6] WCN BASE: wcn_sipc_sblk_notifer  785 index:17  event:2
<6>[    8.081518][T89@C4] sprd-wlan: download the second section of config file
<4>[    8.081582][T89@C4] sprd-wlan: [8067]cid 0 tx[CMD_DOWNLOAD_INI]
<6>[    8.081686][T417@C0] WCN BASE: [wcn_sipc_buf_list_alloc] 298 chn[16]
<6>[    8.081733][T417@C0] WCN BASE: [wcn_sipc_push_list_enqueue] 398 chn[16]
<6>[    8.081751][T417@C0] WCN BASE: [wcn_sipc_wakeup_tx] 326 chn[16]
<6>[    8.081784][T417@C0] WCN BASE: [wcn_sipc_sblk_push] 720 chn[16]
<6>[    8.081796][T485@C6] WCN BASE: [wcn_sipc_work_func] 899 chn[16] during compelte pt:9309(us)
<6>[    8.081853][T485@C6] WCN BASE: [wcn_sipc_sblk_send] 626 chn[16]
<6>[    8.081862][T485@C6] WCN BASE: [wcn_sipc_sblk_push_list_dequeue] 672 chn[16]
<6>[    8.081874][T485@C6] WCN BASE: [wcn_sipc_pop_list_enqueue] 346 chn[16]
<6>[    8.081882][T485@C6] WCN BASE: [wcn_sipc_pop_list_flush] 358 chn[16]
<6>[    8.081898][T485@C6] sprd-wlan: tx_cmd_pop num: 1,cmd_poped=3, cmd_send=3
<6>[    8.081907][T485@C6] WCN BASE: [wcn_sipc_buf_list_free] 312 chn[16]
<6>[    8.081924][T485@C6] WCN BASE: [wcn_sipc_sblk_push_list_dequeue] 687 chn[16]
<6>[    8.089349][T481@C6] WCN BASE: wcn_sipc_sblk_notifer  785 index:17  event:2
<6>[    8.089575][T89@C4] sprd-wlan: download the third section of config file
<4>[    8.089622][T89@C4] sprd-wlan: [8075]cid 0 tx[CMD_DOWNLOAD_INI]
<6>[    8.089774][T417@C0] WCN BASE: [wcn_sipc_buf_list_alloc] 298 chn[16]
<6>[    8.089816][T417@C0] WCN BASE: [wcn_sipc_push_list_enqueue] 398 chn[16]
<6>[    8.089834][T417@C0] WCN BASE: [wcn_sipc_wakeup_tx] 326 chn[16]
<6>[    8.089867][T417@C0] WCN BASE: [wcn_sipc_sblk_push] 720 chn[16]
<6>[    8.089876][T485@C6] WCN BASE: [wcn_sipc_work_func] 899 chn[16] during compelte pt:8068(us)
<6>[    8.089923][T485@C6] WCN BASE: [wcn_sipc_sblk_send] 626 chn[16]
<6>[    8.089933][T485@C6] WCN BASE: [wcn_sipc_sblk_push_list_dequeue] 672 chn[16]
<6>[    8.089973][T485@C6] WCN BASE: [wcn_sipc_pop_list_enqueue] 346 chn[16]
<6>[    8.089981][T485@C6] WCN BASE: [wcn_sipc_pop_list_flush] 358 chn[16]
<6>[    8.089996][T485@C6] sprd-wlan: tx_cmd_pop num: 1,cmd_poped=4, cmd_send=4
<6>[    8.090004][T485@C6] WCN BASE: [wcn_sipc_buf_list_free] 312 chn[16]
<6>[    8.090021][T485@C6] WCN BASE: [wcn_sipc_sblk_push_list_dequeue] 687 chn[16]
<6>[    8.094992][T481@C6] WCN BASE: wcn_sipc_sblk_notifer  785 index:17  event:2
<3>[    8.095184][T415@C3] sprd-wlan: sc2355_rx_rsp_process cid 0 recv rsp[CMD_DOWNLOAD_INI] status[SPRD_CMD_STATUS_UNKNOWN_ERROR]
<6>[    8.095500][T89@C4] sprd-wlan: download the 4th section of config file
<4>[    8.095546][T89@C4] sprd-wlan: [8081]cid 0 tx[CMD_DOWNLOAD_INI]
<6>[    8.095644][T417@C0] WCN BASE: [wcn_sipc_buf_list_alloc] 298 chn[16]
<6>[    8.095688][T417@C0] WCN BASE: [wcn_sipc_push_list_enqueue] 398 chn[16]
<6>[    8.095706][T417@C0] WCN BASE: [wcn_sipc_wakeup_tx] 326 chn[16]
<6>[    8.095740][T417@C0] WCN BASE: [wcn_sipc_sblk_push] 720 chn[16]
<6>[    8.095758][T485@C6] WCN BASE: [wcn_sipc_work_func] 899 chn[16] during compelte pt:5871(us)
<6>[    8.095819][T485@C6] WCN BASE: [wcn_sipc_sblk_send] 626 chn[16]
<6>[    8.095828][T485@C6] WCN BASE: [wcn_sipc_sblk_push_list_dequeue] 672 chn[16]
<6>[    8.095839][T485@C6] WCN BASE: [wcn_sipc_pop_list_enqueue] 346 chn[16]
<6>[    8.095848][T485@C6] WCN BASE: [wcn_sipc_pop_list_flush] 358 chn[16]
<6>[    8.095864][T485@C6] sprd-wlan: tx_cmd_pop num: 1,cmd_poped=5, cmd_send=5
<6>[    8.095873][T485@C6] WCN BASE: [wcn_sipc_buf_list_free] 312 chn[16]
<6>[    8.095890][T485@C6] WCN BASE: [wcn_sipc_sblk_push_list_dequeue] 687 chn[16]
<6>[    8.100055][T481@C6] WCN BASE: wcn_sipc_sblk_notifer  785 index:17  event:2
<6>[    8.100677][T89@C4] sprd-wlan: download the fifth section of config file
<6>[    8.100698][T89@C4] sprd-wlan: ap_oui_num  = 5
<4>[    8.100743][T89@C4] sprd-wlan: [8086]cid 0 tx[CMD_DOWNLOAD_INI]
<6>[    8.100835][T417@C0] WCN BASE: [wcn_sipc_buf_list_alloc] 298 chn[16]
<6>[    8.100881][T417@C0] WCN BASE: [wcn_sipc_push_list_enqueue] 398 chn[16]
<6>[    8.100899][T417@C0] WCN BASE: [wcn_sipc_wakeup_tx] 326 chn[16]
<6>[    8.100932][T417@C0] WCN BASE: [wcn_sipc_sblk_push] 720 chn[16]
<6>[    8.100942][T485@C6] WCN BASE: [wcn_sipc_work_func] 899 chn[16] during compelte pt:5171(us)
<6>[    8.100989][T485@C6] WCN BASE: [wcn_sipc_sblk_send] 626 chn[16]
<6>[    8.100997][T485@C6] WCN BASE: [wcn_sipc_sblk_push_list_dequeue] 672 chn[16]
<6>[    8.101008][T485@C6] WCN BASE: [wcn_sipc_pop_list_enqueue] 346 chn[16]
<6>[    8.101017][T485@C6] WCN BASE: [wcn_sipc_pop_list_flush] 358 chn[16]
<6>[    8.101031][T485@C6] sprd-wlan: tx_cmd_pop num: 1,cmd_poped=6, cmd_send=6
<6>[    8.101039][T485@C6] WCN BASE: [wcn_sipc_buf_list_free] 312 chn[16]
<6>[    8.101056][T485@C6] WCN BASE: [wcn_sipc_sblk_push_list_dequeue] 687 chn[16]
<6>[    8.105936][T481@C6] WCN BASE: wcn_sipc_sblk_notifer  785 index:17  event:2
<6>[    8.106525][T89@C4] sprd-wlan: download the sixth section of config file
<4>[    8.106576][T89@C4] sprd-wlan: [8092]cid 0 tx[CMD_DOWNLOAD_INI]
<6>[    8.106670][T417@C0] WCN BASE: [wcn_sipc_buf_list_alloc] 298 chn[16]
<6>[    8.106714][T417@C0] WCN BASE: [wcn_sipc_push_list_enqueue] 398 chn[16]
<6>[    8.106732][T417@C0] WCN BASE: [wcn_sipc_wakeup_tx] 326 chn[16]
<6>[    8.106771][T417@C0] WCN BASE: [wcn_sipc_sblk_push] 720 chn[16]
<6>[    8.106787][T485@C6] WCN BASE: [wcn_sipc_work_func] 899 chn[16] during compelte pt:5834(us)
<6>[    8.106841][T485@C6] WCN BASE: [wcn_sipc_sblk_send] 626 chn[16]
<6>[    8.106850][T485@C6] WCN BASE: [wcn_sipc_sblk_push_list_dequeue] 672 chn[16]
<6>[    8.106862][T485@C6] WCN BASE: [wcn_sipc_pop_list_enqueue] 346 chn[16]
<6>[    8.106871][T485@C6] WCN BASE: [wcn_sipc_pop_list_flush] 358 chn[16]
<6>[    8.106888][T485@C6] sprd-wlan: tx_cmd_pop num: 1,cmd_poped=7, cmd_send=7
<6>[    8.106898][T485@C6] WCN BASE: [wcn_sipc_buf_list_free] 312 chn[16]
<6>[    8.106915][T485@C6] WCN BASE: [wcn_sipc_sblk_push_list_dequeue] 687 chn[16]
<6>[    8.112223][T481@C6] WCN BASE: wcn_sipc_sblk_notifer  785 index:17  event:2
<4>[    8.112643][T89@C4] sprd-wlan: [8098]cid 0 tx[CMD_GET_INFO]
<6>[    8.112749][T417@C0] WCN BASE: [wcn_sipc_buf_list_alloc] 298 chn[16]
<6>[    8.112796][T417@C0] WCN BASE: [wcn_sipc_push_list_enqueue] 398 chn[16]
<6>[    8.112814][T417@C0] WCN BASE: [wcn_sipc_wakeup_tx] 326 chn[16]
<6>[    8.112853][T417@C0] WCN BASE: [wcn_sipc_sblk_push] 720 chn[16]
<6>[    8.112867][T485@C6] WCN BASE: [wcn_sipc_work_func] 899 chn[16] during compelte pt:6066(us)
<6>[    8.112923][T485@C6] WCN BASE: [wcn_sipc_sblk_send] 626 chn[16]
<6>[    8.112933][T485@C6] WCN BASE: [wcn_sipc_sblk_push_list_dequeue] 672 chn[16]
<6>[    8.112945][T485@C6] WCN BASE: [wcn_sipc_pop_list_enqueue] 346 chn[16]
<6>[    8.112954][T485@C6] WCN BASE: [wcn_sipc_pop_list_flush] 358 chn[16]
<6>[    8.112971][T485@C6] sprd-wlan: tx_cmd_pop num: 1,cmd_poped=8, cmd_send=8
<6>[    8.112981][T485@C6] WCN BASE: [wcn_sipc_buf_list_free] 312 chn[16]
<6>[    8.113000][T485@C6] WCN BASE: [wcn_sipc_sblk_push_list_dequeue] 687 chn[16]
<6>[    8.116790][T481@C6] WCN BASE: wcn_sipc_sblk_notifer  785 index:17  event:2
<6>[    8.117222][T89@C4] sprd-wlan: sc2355_get_fw_info, drv_ver=2, fw_ver=2, compat_ver=0, ap_ver=1
<6>[    8.117249][T89@C4] sprd-wlan: chip_model:0x2355, chip_ver:0x0
<6>[    8.117265][T89@C4] sprd-wlan: fw_ver:59279, fw_std:0x7f, fw_capa:0x120f7f
<6>[    8.117283][T89@C4] sprd-wlan: credit_capa:TX_WITH_CREDIT
<6>[    8.117300][T89@C4] sprd-wlan: ott support:0
<6>[    8.118063][T89@C4] sprd-wlan: sc2355_reg_notify 00 initiator 0 hint_type 0
<4>[    8.118129][T89@C4] sprd-wlan: [8104]cid 0 tx[CMD_SET_REGDOM]
<6>[    8.118247][T417@C3] WCN BASE: [wcn_sipc_buf_list_alloc] 298 chn[16]
<6>[    8.118292][T417@C3] WCN BASE: [wcn_sipc_push_list_enqueue] 398 chn[16]
<6>[    8.118310][T417@C3] WCN BASE: [wcn_sipc_wakeup_tx] 326 chn[16]
<6>[    8.118367][T485@C6] WCN BASE: [wcn_sipc_work_func] 899 chn[16] during compelte pt:5488(us)
<6>[    8.118370][T417@C3] WCN BASE: [wcn_sipc_sblk_push] 720 chn[16]
<6>[    8.118423][T485@C6] WCN BASE: [wcn_sipc_sblk_send] 626 chn[16]
<6>[    8.118432][T485@C6] WCN BASE: [wcn_sipc_sblk_push_list_dequeue] 672 chn[16]
<6>[    8.118443][T485@C6] WCN BASE: [wcn_sipc_pop_list_enqueue] 346 chn[16]
<6>[    8.118453][T485@C6] WCN BASE: [wcn_sipc_pop_list_flush] 358 chn[16]
<6>[    8.118469][T485@C6] sprd-wlan: tx_cmd_pop num: 1,cmd_poped=9, cmd_send=9
<6>[    8.118478][T485@C6] WCN BASE: [wcn_sipc_buf_list_free] 312 chn[16]
<6>[    8.118496][T485@C6] WCN BASE: [wcn_sipc_sblk_push_list_dequeue] 687 chn[16]
<6>[    8.128855][T481@C2] WCN BASE: wcn_sipc_sblk_notifer  785 index:17  event:2
<6>[    8.129139][T89@C4] sprd-wlan: power_save [SET_POWER_BACKOFF]
<4>[    8.129176][T89@C4] sprd-wlan: [8115]cid 0 tx[CMD_POWER_SAVE]
<6>[    8.129270][T417@C3] WCN BASE: [wcn_sipc_buf_list_alloc] 298 chn[16]
<6>[    8.129314][T417@C3] WCN BASE: [wcn_sipc_push_list_enqueue] 398 chn[16]
<6>[    8.129332][T417@C3] WCN BASE: [wcn_sipc_wakeup_tx] 326 chn[16]
<6>[    8.129368][T417@C3] WCN BASE: [wcn_sipc_sblk_push] 720 chn[16]
<6>[    8.129397][T485@C2] WCN BASE: [wcn_sipc_work_func] 899 chn[16] during compelte pt:11016(us)
<6>[    8.129497][T485@C2] WCN BASE: [wcn_sipc_sblk_send] 626 chn[16]
<6>[    8.129514][T485@C2] WCN BASE: [wcn_sipc_sblk_push_list_dequeue] 672 chn[16]
<6>[    8.129538][T485@C2] WCN BASE: [wcn_sipc_pop_list_enqueue] 346 chn[16]
<6>[    8.129555][T485@C2] WCN BASE: [wcn_sipc_pop_list_flush] 358 chn[16]
<6>[    8.129586][T485@C2] sprd-wlan: tx_cmd_pop num: 1,cmd_poped=10, cmd_send=10
<6>[    8.129604][T485@C2] WCN BASE: [wcn_sipc_buf_list_free] 312 chn[16]
<6>[    8.129639][T485@C2] WCN BASE: [wcn_sipc_sblk_push_list_dequeue] 687 chn[16]
<6>[    8.133243][T481@C2] WCN BASE: wcn_sipc_sblk_notifer  785 index:17  event:2
<4>[    8.134472][T89@C4] (unnamed net_device) (uninitialized): iface_set_mac_addr Warning: use random MAC address
<6>[    8.137251][T89@C4] sprd-wlan: 	IPV6 NS Offload supported
<6>[    8.137284][T89@C4] sprd-wlan: sprd_iface_set_power Power off WCN (1 time)
<6>[    8.137331][T89@C4] WCN BASE: [wcn_sipc_chn_deinit]:index[17] chn[7], sipc_chn->ops = null.
<6>[    8.137350][T89@C4] WCN BUS: [+]bus_chn_deinit(17, 2)
<6>[    8.137373][T89@C4] WCN BUS: [-]bus_chn_deinit(17)
<6>[    8.137389][T89@C4] WCN BASE: sipc chn[17] deinit success!
<6>[    8.137406][T89@C4] WCN BASE: [wcn_sipc_chn_deinit]:index[19] chn[8], sipc_chn->ops = null.
<6>[    8.137423][T89@C4] WCN BUS: [+]bus_chn_deinit(19, 2)
<6>[    8.137441][T89@C4] WCN BUS: [-]bus_chn_deinit(19)
<6>[    8.137457][T89@C4] WCN BASE: sipc chn[19] deinit success!
<6>[    8.137473][T89@C4] WCN BASE: [wcn_sipc_chn_deinit]:index[21] chn[9], sipc_chn->ops = null.
<6>[    8.137489][T89@C4] WCN BUS: [+]bus_chn_deinit(21, 2)
<6>[    8.137507][T89@C4] WCN BUS: [-]bus_chn_deinit(21)
<6>[    8.137523][T89@C4] WCN BASE: sipc chn[21] deinit success!
<6>[    8.137540][T89@C4] WCN BASE: [wcn_sipc_chn_deinit]:index[16] chn[7], sipc_chn->ops = null.
<6>[    8.137556][T89@C4] WCN BASE: Wait 3-7 index16 push
<6>[    8.137573][T89@C4] WCN BUS: [+]bus_chn_deinit(16, 2)
<6>[    8.137590][T89@C4] WCN BUS: [-]bus_chn_deinit(16)
<6>[    8.137606][T89@C4] WCN BASE: sipc chn[16] deinit success!
<6>[    8.137622][T89@C4] WCN BASE: [wcn_sipc_chn_deinit]:index[18] chn[8], sipc_chn->ops = null.
<6>[    8.137639][T89@C4] WCN BASE: Wait 3-8 index18 push
<6>[    8.137655][T89@C4] WCN BUS: [+]bus_chn_deinit(18, 2)
<6>[    8.137673][T89@C4] WCN BUS: [-]bus_chn_deinit(18)
<3>[    8.137688][T89@C4] WCN BASE error: chn status(0)! sipc_chn(8)
<3>[    8.137799][T482@C7] unisoc-mailbox channel 3-8 smsg receive error = -5!
<3>[    8.137813][T482@C7] sprd-sblock: Failed to open channel 8
<6>[    8.138050][T89@C4] WCN BASE: sipc chn[18] deinit and destroy!
<6>[    8.138070][T89@C4] WCN BASE: sipc chn[18] deinit success!
<6>[    8.138087][T89@C4] WCN BASE: [wcn_sipc_chn_deinit]:index[20] chn[9], sipc_chn->ops = null.
<6>[    8.138105][T89@C4] WCN BASE: Wait 3-9 index20 push
<6>[    8.138122][T89@C4] WCN BUS: [+]bus_chn_deinit(20, 2)
<6>[    8.138139][T89@C4] WCN BUS: [-]bus_chn_deinit(20)
<3>[    8.138156][T89@C4] WCN BASE error: chn status(0)! sipc_chn(9)
<3>[    8.138339][T483@C6] unisoc-mailbox channel 3-9 smsg receive error = -5!
<3>[    8.138352][T483@C6] sprd-sblock: Failed to open channel 9
<6>[    8.138475][T89@C4] WCN BASE: sipc chn[20] deinit and destroy!
<6>[    8.138492][T89@C4] WCN BASE: sipc chn[20] deinit success!
<6>[    8.138508][T89@C4] sprd-wlan: reinit hang(1) thermal(0) suspend(0) status to default
<6>[    8.138526][T89@C4] WCN BOOT: : stop subsys:2
<6>[    8.138615][T89@C4] WCN BOOT: : before stop malrin status[4] BT:0 FM:0 WIFI:4 MDBG:0
<6>[    8.138635][T89@C4] WCN BOOT: : before stop gnss status[0] GPS:0 GNSS_BD:0 GNSS_GAL:0
<6>[    8.138653][T89@C4] WCN BOOT: : wcn_btwf,subsys=2 do stop
<6>[    8.138749][T89@C4] WCN BASE: poweroff_state before  btwf clear : 0x70-0x11000000
<6>[    8.138767][T89@C4] WCN BASE: btwf_record_gnss_current_clk 0
<6>[    8.138783][T89@C4] WCN BASE: btwf_pwr_state 0
<6>[    8.138799][T89@C4] WCN BASE: btwf_dfs_init 1
<6>[    8.138814][T89@C4] WCN BASE: btwf_dfs_active 1
<6>[    8.138830][T89@C4] WCN BASE: btwf_spinlock 0
<6>[    8.138845][T89@C4] WCN BASE: gnss_clk_req_ack 0
<6>[    8.138861][T89@C4] WCN BASE: gnss_pwr_state 0
<6>[    8.138877][T89@C4] WCN BASE: gnss_dfs_active 0
<6>[    8.138893][T89@C4] WCN BASE: gnss_spinlock 0
<6>[    8.138909][T89@C4] WCN BASE: wcn_dfs_info: 0x60-0x11000000-0x10006150-0x0
<6>[    8.138926][T89@C4] WCN BASE: poweroff_state after btwf clear:
<6>[    8.138941][T89@C4] WCN BASE: btwf_record_gnss_current_clk 0
<6>[    8.138957][T89@C4] WCN BASE: btwf_pwr_state 0
<6>[    8.138972][T89@C4] WCN BASE: btwf_dfs_init 1
<6>[    8.138987][T89@C4] WCN BASE: btwf_dfs_active 1
<6>[    8.139002][T89@C4] WCN BASE: btwf_spinlock 0
<6>[    8.139018][T89@C4] WCN BASE: gnss_clk_req_ack 0
<6>[    8.139033][T89@C4] WCN BASE: gnss_pwr_state 0
<6>[    8.139048][T89@C4] WCN BASE: gnss_dfs_active 0
<6>[    8.139064][T89@C4] WCN BASE: gnss_spinlock 0
<6>[    8.139079][T89@C4] WCN BASE: wcn_dfs_info: 0x60-0x11000000-0x10006150-0x0
<6>[    8.139097][T89@C4] WCN BASE: stop_loopcheck
<6>[    8.139134][T89@C4] WCN BOOT: : [+]btwf_sys_is_deepsleep_status?
<6>[    8.139168][T89@C4] WCN BOOT: : REG 0x64000364:val=0x81!
<6>[    8.139184][T89@C4] WCN BOOT: : btwf isn't deep sleep!
<6>[    8.140764][T89@C4] WCN BOOT: : [+]btwf_sys_is_deepsleep_status?
<6>[    8.140798][T89@C4] WCN BOOT: : REG 0x64000364:val=0x301!
<6>[    8.140817][T89@C4] WCN BOOT: : btwf isn't deep sleep!
<6>[    8.140995][T89@C4] WCN BOOT: : [+]btwf_sys_is_deepsleep_status?
<6>[    8.141025][T89@C4] WCN BOOT: : REG 0x64000364:val=0x301!
<6>[    8.141041][T89@C4] WCN BOOT: : btwf isn't deep sleep!
<6>[    8.142365][T89@C4] WCN BOOT: : [+]btwf_sys_is_deepsleep_status?
<6>[    8.142400][T89@C4] WCN BOOT: : REG 0x64000364:val=0x301!
<6>[    8.142417][T89@C4] WCN BOOT: : btwf isn't deep sleep!
<6>[    8.151533][T89@C4] WCN BOOT: : [+]btwf_sys_is_deepsleep_status?
<6>[    8.151585][T89@C4] WCN BOOT: : REG 0x64000364:val=0x1!
<6>[    8.151613][T89@C4] WCN BOOT: : REG 0x64000364:val=0x1!
<6>[    8.151640][T89@C4] WCN BOOT: : REG 0x64000364:val=0x1!
<6>[    8.151656][T89@C4] WCN BOOT: : btwf is deepsleep!
<6>[    8.151672][T89@C4] WCN BOOT: : btwf sys is deepsleep i=4!
<6>[    8.151692][T89@C4] WCN BOOT: : [+]wcn_sys_forbid_deep_sleep
<6>[    8.151709][T89@C4] WCN BOOT: : [+]btwf_clear_force_deepsleep_aontop
<6>[    8.151747][T89@C4] WCN BOOT: : Write 0x64000350:val=0xc00080!
<6>[    8.151763][T89@C4] WCN BOOT: : [+]btwf_sys_force_exit_deep_sleep
<6>[    8.151789][T89@C4] WCN BOOT: : REG 0x6400034c:val=0x10000!
<6>[    8.151824][T89@C4] WCN BOOT: : Set REG 0x6400034c:val=0x0(exit deep)!
<6>[    8.166724][T89@C4] WCN BOOT: : [-]btwf_sys_force_exit_deep_sleep
<6>[    8.166760][T89@C4] WCN BOOT: : [+]btwf_sys_is_wakeup_status?
<6>[    8.166792][T89@C4] WCN BOOT: : REG 0x64000364:val=0x301!
<6>[    8.167156][T89@C4] WCN BOOT: : REG 0x64000364:val=0x301!
<6>[    8.167184][T89@C4] WCN BOOT: : REG 0x64000364:val=0x301!
<6>[    8.167201][T89@C4] WCN BOOT: : btwf is wakeup!
<6>[    8.167217][T89@C4] WCN BOOT: : btwf sys is wakeup i=0!
<6>[    8.167234][T89@C4] WCN BOOT: : [+]btwf_sys_is_poweron_status
<6>[    8.167260][T89@C4] WCN BOOT: : REG 0x64000360:val=0xc0739ce7!
<6>[    8.167286][T89@C4] WCN BOOT: : REG 0x64000360:val=0xc0739ce7!
<6>[    8.167312][T89@C4] WCN BOOT: : REG 0x64000360:val=0xc0739ce7!
<6>[    8.167328][T89@C4] WCN BOOT: : btwf is poweron!
<6>[    8.167343][T89@C4] WCN BOOT: : btwf sys is poweron i=0!
<6>[    8.167359][T89@C4] WCN BOOT: : [+]wcn_sys_is_wakeup_status?
<6>[    8.167386][T89@C4] WCN BOOT: : REG 0x64020860:val=0x60000666!
<6>[    8.167412][T89@C4] WCN BOOT: : REG 0x64020860:val=0x60000666!
<6>[    8.167437][T89@C4] WCN BOOT: : REG 0x64020860:val=0x60000666!
<6>[    8.167453][T89@C4] WCN BOOT: : wcn is wakeup!
<6>[    8.167469][T89@C4] WCN BOOT: : wcn sys wakeup i=0!
<6>[    8.167485][T89@C4] WCN BOOT: : [+]wcn_sys_is_poweron_status
<6>[    8.167511][T89@C4] WCN BOOT: : REG 0x64020538:val=0x0!
<6>[    8.167537][T89@C4] WCN BOOT: : REG 0x64020538:val=0x0!
<6>[    8.167562][T89@C4] WCN BOOT: : REG 0x64020538:val=0x0!
<6>[    8.167578][T89@C4] WCN BOOT: : wcn is poweron!
<6>[    8.167593][T89@C4] WCN BOOT: : wcn sys poweron i=0!
<6>[    8.167621][T89@C4] WCN BOOT: : REG 0x408800c4:val=0x3ffc, allow=0!
<6>[    8.167660][T89@C4] WCN BOOT: : Set REG 0x408800c4:val=0x0(wcn stop ip)!
<6>[    8.167676][T89@C4] WCN BOOT: : [+]btwf_sys_clear_force_exit_deep_sleep
<6>[    8.167700][T89@C4] WCN BOOT: : REG 0x6400034c:val=0x0!
<6>[    8.167732][T89@C4] WCN BOOT: : Set REG 0x6400034c:val=0x10000(exit deep)!
<6>[    8.167749][T89@C4] WCN BOOT: : [+]btwf_sys_shutdown
<6>[    8.167807][T89@C4] WCN BOOT: : btwf_sys_wait_cp2_wfi BTWF CP2 deepsleep:yes
<6>[    8.167835][T89@C4] WCN BOOT: : REG 0x4080c098:val=0x4040002!
<6>[    8.167871][T89@C4] WCN BOOT: : Set REG 0x4080c098:val=0x4041002(auto shutdown)!
<6>[    8.167888][T89@C4] WCN BOOT: : [+]btwf_sys_is_deepsleep_status?
<6>[    8.167913][T89@C4] WCN BOOT: : REG 0x64000364:val=0x1!
<6>[    8.167939][T89@C4] WCN BOOT: : REG 0x64000364:val=0x1!
<6>[    8.167965][T89@C4] WCN BOOT: : REG 0x64000364:val=0x1!
<6>[    8.167980][T89@C4] WCN BOOT: : btwf is deepsleep!
<6>[    8.167996][T89@C4] WCN BOOT: : btwf sys is deepsleep i=0!
<6>[    8.168012][T89@C4] WCN BOOT: : [+]btwf_sys_is_powerdown_status
<6>[    8.168038][T89@C4] WCN BOOT: : REG 0x64000360:val=0xce739ce7!
<6>[    8.168063][T89@C4] WCN BOOT: : REG 0x64000360:val=0xce739ce7!
<6>[    8.168088][T89@C4] WCN BOOT: : REG 0x64000360:val=0xce739ce7!
<6>[    8.168104][T89@C4] WCN BOOT: : btwf is powerdown!
<6>[    8.168120][T89@C4] WCN BOOT: : btwf sys is powerdown i=0!
<6>[    8.168137][T89@C4] WCN BASE: wcn_sipc_chn_set_status chn: 4 ,  flag:0
<6>[    8.168154][T89@C4] WCN BASE: wcn_sipc_chn_set_status chn: 4 ,  flag:0
<6>[    8.168170][T89@C4] WCN BASE: wcn_sipc_chn_set_status chn: 11 ,  flag:0
<6>[    8.168188][T89@C4] WCN BASE: wcn_sipc_chn_set_status chn: 12 ,  flag:0
<6>[    8.168204][T89@C4] WCN BASE: wcn_sipc_chn_set_status chn: 5 ,  flag:0
<6>[    8.168220][T89@C4] WCN BASE: wcn_sipc_chn_set_status chn: 4 ,  flag:0
<6>[    8.168236][T89@C4] WCN BASE: wcn_sipc_chn_set_status chn: 4 ,  flag:0
<6>[    8.168252][T89@C4] WCN BASE: wcn_sipc_chn_set_status chn: 4 ,  flag:0
<6>[    8.168268][T89@C4] WCN BASE: wcn_sipc_chn_set_status chn: 4 ,  flag:0
<6>[    8.168284][T89@C4] WCN BASE: wcn_sipc_chn_set_status chn: 7 ,  flag:0
<6>[    8.168300][T89@C4] WCN BASE: wcn_sipc_chn_set_status chn: 7 ,  flag:0
<6>[    8.168316][T89@C4] WCN BASE: wcn_sipc_chn_set_status chn: 8 ,  flag:0
<6>[    8.168333][T89@C4] WCN BASE: wcn_sipc_chn_set_status chn: 8 ,  flag:0
<6>[    8.168349][T89@C4] WCN BASE: wcn_sipc_chn_set_status chn: 9 ,  flag:0
<6>[    8.168365][T89@C4] WCN BASE: wcn_sipc_chn_set_status chn: 9 ,  flag:0
<6>[    8.168391][T89@C4] WCN BOOT: : REG 0x4088000c:val=0x2!
<6>[    8.168427][T89@C4] WCN BOOT: : Set REG 0x4088000c:val=0x57(cpu...reset)!
<6>[    8.168453][T89@C4] WCN BOOT: : REG 0x4080c0320:val=0xca!
<6>[    8.168489][T89@C4] WCN BOOT: : Set REG 0x4080c0320:val=0xea(vol adj en)!
<6>[    8.168507][T89@C4] WCN BASE: wcn_subsys_active_num, 0
<6>[    8.168526][T89@C4] WCN BOOT: : [+]wcn_sys_power_down
<6>[    8.168562][T89@C4] WCN BOOT: : REG 0x640203a8:val=0x1020406(auto shutdown set)!
<6>[    8.168588][T89@C4] WCN BOOT: : REG 0x408800c4:val=0x0, allow=1!
<6>[    8.168814][T89@C4] WCN BOOT: : wcn is shutdown!(i=3, 0x64020538:0x7000000)
<6>[    8.168834][T89@C4] WCN BOOT: : wcn sys shutdown i=1!
<6>[    8.168850][T89@C4] WCN BOOT: : [-]wcn_sys_power_down
<6>[    8.168867][T89@C4] WCN BOOT: : [+]wcn_sys_power_clock_unsupport
<6>[    8.169362][T89@C4] WCN BASE: enable = 0, ret = 0
<6>[    8.198000][T89@C4] WCN BASE: merlion chip en pull down
<6>[    8.198055][T89@C4] WCN BASE: set WCN SYS TOP PD
<6>[    8.198071][T89@C4] WCN BASE: enable = 0, btwf_open=0, gnss_open=0
<6>[    8.198443][T89@C4] WCN BASE: enable=0,en_count=0,ret=0,btwf=0,gnss=0
<6>[    8.198826][T89@C4] WCN BASE: enable=0,en_count=0,ret=0,btwf=0,gnss=0
<6>[    8.198848][T89@C4] WCN BOOT: : [-]wcn_sys_power_clock_unsupport
<6>[    8.198939][T89@C4] WCN BASE: [-]wcn_rfi_status_clear:status=1
<6>[    8.199020][T89@C4] WCN BASE: poweroff_shutdown before btwf clear: 0x60-0x11000000
<6>[    8.199038][T89@C4] WCN BASE: btwf_record_gnss_current_clk 0
<6>[    8.199055][T89@C4] WCN BASE: btwf_pwr_state 0
<6>[    8.199072][T89@C4] WCN BASE: btwf_dfs_init 0
<6>[    8.199087][T89@C4] WCN BASE: btwf_dfs_active 0
<6>[    8.199104][T89@C4] WCN BASE: btwf_spinlock 0
<6>[    8.199119][T89@C4] WCN BASE: gnss_clk_req_ack 0
<6>[    8.199135][T89@C4] WCN BASE: gnss_pwr_state 0
<6>[    8.199151][T89@C4] WCN BASE: gnss_dfs_active 0
<6>[    8.199167][T89@C4] WCN BASE: gnss_spinlock 0
<6>[    8.199182][T89@C4] WCN BASE: wcn_dfs_info: 0x0-0x11000000-0x10006150-0x0
<6>[    8.199199][T89@C4] WCN BASE: poweroff_shutdown after btwf clear:
<6>[    8.199215][T89@C4] WCN BASE: btwf_record_gnss_current_clk 0
<6>[    8.199230][T89@C4] WCN BASE: btwf_pwr_state 0
<6>[    8.199246][T89@C4] WCN BASE: btwf_dfs_init 0
<6>[    8.199261][T89@C4] WCN BASE: btwf_dfs_active 0
<6>[    8.199277][T89@C4] WCN BASE: btwf_spinlock 0
<6>[    8.199292][T89@C4] WCN BASE: gnss_clk_req_ack 0
<6>[    8.199308][T89@C4] WCN BASE: gnss_pwr_state 0
<6>[    8.199324][T89@C4] WCN BASE: gnss_dfs_active 0
<6>[    8.199340][T89@C4] WCN BASE: gnss_spinlock 0
<6>[    8.199356][T89@C4] WCN BASE: wcn_dfs_info: 0x0-0x11000000-0x10006150-0x0
<6>[    8.199373][T89@C4] WCN BOOT: : wcn_btwf open_status = 0,power_state=0,stop subsys=2!
<6>[    8.199392][T89@C4] WCN BOOT: : [+]btwf_clear_force_deepsleep_aontop
<6>[    8.199434][T89@C4] WCN BOOT: : Write 0x64000350:val=0xc00080!
<6>[    8.199452][T89@C4] WCN BOOT: : [+]btwf_clear_force_shutdown_aontop
<6>[    8.199488][T89@C4] WCN BOOT: : Write 0x64000350:val=0xc00080!
<6>[    8.199504][T89@C4] WCN BASE: cp2 power status:0
<6>[    8.199572][T89@C4] WCN BOOT: : after stop2 malrin status[0] BT:0 FM:0 WIFI:0 MDBG:0
<6>[    8.199591][T89@C4] WCN BOOT: : after stop2 gnss status[0] GPS:0 GNSS_BD:0 GNSS_GAL:0
<6>[    8.591345][T24@C0] android_work: sent uevent USB_STATE=CONNECTED
<6>[    8.743325][T24@C0] android_work: sent uevent USB_STATE=CONFIGURED
<6>[    8.816367][T334@C4] [78 / 86] [    8.816087] [sprd_tcpm_log] state change SNK_HARD_RESET_SINK_OFF -> SNK_HARD_RESET_SINK_ON [delayed 1925 ms]
<6>[    8.816402][T334@C4] [79 / 86] [    8.816187] [sprd_tcpm_log] state change SNK_HARD_RESET_SINK_ON -> SNK_STARTUP
<6>[    8.816420][T334@C4] [80 / 86] [    8.816191] [sprd_tcpm_log] state change SNK_STARTUP -> SNK_DISCOVERY
<6>[    8.816438][T334@C4] [81 / 86] [    8.816193] [sprd_tcpm_log] Setting voltage/current limit 5000 mV 0 mA
<6>[    8.816456][T334@C4] [82 / 86] [    8.816196] [sprd_tcpm_log] state change SNK_DISCOVERY -> SNK_WAIT_CAPABILITIES
<6>[    8.816473][T334@C4] [83 / 86] [    8.816198] [sprd_pd_log] sc27xx_pd_reset:line784: clear_tx_id = 0
<6>[    8.816491][T334@C4] [84 / 86] [    8.816201] [sprd_pd_log] sc27xx_pd_rx_flush:line761: start rx flush
<6>[    8.816508][T334@C4] [85 / 86] [    8.816294] [sprd_pd_log] sc27xx_pd_rx_flush:line764: rx flush done
<6>[    8.816826][T335@C7] sc27xx-typec-pd 64200000.spi:pmic@0:pd@e00: pd := on
<6>[    8.842388][T334@C4] [86 / 89] [    8.816516] [sprd_pd_log] set rx: on = 1, sts1 = 0x2000
<6>[    8.842418][T334@C4] [87 / 89] [    8.816822] [sprd_pd_log] set rx: pd := on
<6>[    8.842436][T334@C4] [88 / 89] [    8.816841] [sprd_tcpm_log] pending state change SNK_WAIT_CAPABILITIES -> HARD_RESET_SEND @ 460 ms
<6>[    9.277375][T334@C4] [89 / 95] [    9.277112] [sprd_tcpm_log] state change SNK_WAIT_CAPABILITIES -> HARD_RESET_SEND [delayed 460 ms]
<6>[    9.277412][T334@C4] [90 / 95] [    9.277166] [sprd_tcpm_log] PD TX, type: 0x5
<6>[    9.277430][T334@C4] [91 / 95] [    9.277183] [sprd_pd_log] sc27xx_pd_send_hardreset:line815: send hard reset
<6>[    9.277447][T334@C4] [92 / 95] [    9.277193] [sprd_pd_log] sc27xx_pd_reset:line784: clear_tx_id = 1
<6>[    9.277465][T334@C4] [93 / 95] [    9.277195] [sprd_pd_log] sc27xx_pd_rx_flush:line761: start rx flush
<6>[    9.277485][T334@C4] [94 / 95] [    9.277311] [sprd_pd_log] sc27xx_pd_rx_flush:line764: rx flush done
<6>[    9.277676][T335@C7] charger-manager charger-manager: sprd_vchg: receive USB PD hard reset request
<4>[    9.277808][T335@C7] sc27xx-typec-pd 64200000.spi:pmic@0:pd@e00: IRQ: PD send hardreset, ktime = 9263 ms
<6>[    9.302419][T334@C4] [95 / 97] [    9.277483] [sprd_pd_log] sc27xx_pd_reset:line799: clear tx id
<6>[    9.302452][T334@C4] [96 / 97] [    9.277805] [sprd_pd_log] PD send hardreset, ktime = 9263 ms
<6>[    9.478277][T335@C7] sc27xx-typec 64200000.spi:pmic@0:typec@380: sc27xx_typec_pr_swap_no_chk_detach, on = 0
<6>[    9.478519][T334@C4] [97 / 103] [    9.478245] [sprd_tcpm_log] PD TX time out
<6>[    9.478543][T334@C4] [98 / 103] [    9.478267] [sprd_tcpm_log] state change HARD_RESET_SEND -> HARD_RESET_START
<6>[    9.478561][T334@C4] [99 / 103] [    9.478270] [sprd_tcpm_log] sprd_tcpm_typec_pr_swap_no_chk_detach, cur state: HARD_RESET_START, on: 0
<6>[    9.478578][T334@C4] [100 / 103] [    9.478366] [sprd_pd_log] sc27xx_pd_reset:line784: clear_tx_id = 0
<6>[    9.478597][T334@C4] [101 / 103] [    9.478377] [sprd_pd_log] sc27xx_pd_rx_flush:line761: start rx flush
<6>[    9.478614][T334@C4] [102 / 103] [    9.478471] [sprd_pd_log] sc27xx_pd_rx_flush:line764: rx flush done
<6>[    9.478772][T334@C4] [103 / 104] [    9.478734] [sprd_pd_log] set rx: on = 0, sts1 = 0x2000
<6>[    9.479025][T335@C7] sc27xx-typec-pd 64200000.spi:pmic@0:pd@e00: pd := off
<6>[    9.479062][T334@C4] [104 / 106] [    9.479008] [sprd_pd_log] set rx: pd := off
<4>[    9.479065][T335@C7] sc27xx-typec-pd 64200000.spi:pmic@0:pd@e00: vconn NULL!!!
<6>[    9.479081][T334@C4] [105 / 106] [    9.479040] [sprd_tcpm_log] state change HARD_RESET_START -> SNK_HARD_RESET_SINK_OFF
<6>[    9.506420][T334@C4] [106 / 110] [    9.479061] [sprd_tcpm_log] vconn:=0
<6>[    9.506447][T334@C4] [107 / 110] [    9.479076] [sprd_tcpm_log] sprd_tcpm_set_roles:line953 set roles [sink:device]
<6>[    9.506467][T334@C4] [108 / 110] [    9.479078] [sprd_tcpm_log] Requesting mux state 1, usb-role 2, orientation 2
<6>[    9.506490][T334@C4] [109 / 110] [    9.479136] [sprd_tcpm_log] pending state change SNK_HARD_RESET_SINK_OFF -> SNK_HARD_RESET_SINK_ON @ 1925 ms
<14>[   10.231522][ T1@C6] init: starting service 'srmi_proxyd'...
<14>[   10.244044][ T1@C6] init: ... started service 'srmi_proxyd' has pid 515
<3>[   10.296326][T269@C5] |__i2s     mmc0: 2954   83    6    4    0    0    0    0    0    0    0    0    0    0
<3>[   10.296356][T269@C5] |__i2e     mmc0: 2719  274   25   27    2    0    0    0    0    0    0    0    0    0
<3>[   10.296367][T269@C5] |__swcq    mmc0: cmdq_mode= 0, recovery_cnt= 0, qcnt= 1, cmdq_cnt= 0
<3>[   10.296373][T269@C5] |__swcq    mmc0: cmd resp err cnt 0, 0, 0, 0, 0, 0, 0
<3>[   10.301281][T24@C0] |__c2e     mmc0: 1855    0    0    0    0    0    0    0    0    0    0    0    0    0
<3>[   10.301317][T24@C0] |__d2e     mmc0: 1664  112    1   12    0    0    0    0    0    0    0    0    0    0
<3>[   10.301341][T24@C0] |__blocks  mmc0:    0    0    2    0  523  359  210  291  108  160   47   89    0    0
<3>[   10.301370][T24@C0] |__speed   mmc0: r= 162.96M/s, w= 210.89M/s, r_blk= 217428, w_blk= 3600
<6>[   10.301472][    C5] |_mmcblk0    total complete 1211 requests
<6>[   10.301483][    C5] |_mmcblk0    R complete:   1094 request   184272 sectors
<6>[   10.301489][    C5] |_mmcblk0    R i2i[  8- 8192ms]: 1094    0    0    0    0    0    0    0    0    0    0    0
<6>[   10.301500][    C5] |_mmcblk0    R i2c[  8- 8192ms]: 1092    2    0    0    0    0    0    0    0    0    0    0
<6>[   10.301511][    C5] |_mmcblk0    W complete:     94 request     3568 sectors
<6>[   10.301515][    C5] |_mmcblk0    W i2i[  8- 8192ms]:   94    0    0    0    0    0    0    0    0    0    0    0
<6>[   10.301525][    C5] |_mmcblk0    W i2c[  8- 8192ms]:   94    0    0    0    0    0    0    0    0    0    0    0
<6>[   10.301534][    C5] |_mmcblk0    F complete:     23 request        0 sectors
<6>[   10.301539][    C5] |_mmcblk0    F i2i[  8- 8192ms]:   23    0    0    0    0    0    0    0    0    0    0    0
<6>[   10.301548][    C5] |_mmcblk0    F i2c[  8- 8192ms]:   23    0    0    0    0    0    0    0    0    0    0    0
<6>[   10.301557][    C5] |_loop       total complete 88 requests
<6>[   10.301560][    C5] |_loop       R complete:     88 request     7864 sectors
<6>[   10.301564][    C5] |_loop       R i2i[  8- 8192ms]:   88    0    0    0    0    0    0    0    0    0    0    0
<6>[   10.301573][    C5] |_loop       R i2c[  8- 8192ms]:   87    1    0    0    0    0    0    0    0    0    0    0
<14>[   10.307214][T291@C0] servicemanager: Caller(pid=515,uid=0,sid=u:r:srmi_proxyd:s0) Found vendor.unisoc.frameworks.srmi.ISrmiManager/default in framework VINTF manifest.
<6>[   11.404704][T334@C4] [110 / 118] [   11.404429] [sprd_tcpm_log] state change SNK_HARD_RESET_SINK_OFF -> SNK_HARD_RESET_SINK_ON [delayed 1925 ms]
<6>[   11.404741][T334@C4] [111 / 118] [   11.404531] [sprd_tcpm_log] state change SNK_HARD_RESET_SINK_ON -> SNK_STARTUP
<6>[   11.404759][T334@C4] [112 / 118] [   11.404535] [sprd_tcpm_log] state change SNK_STARTUP -> SNK_DISCOVERY
<6>[   11.404776][T334@C4] [113 / 118] [   11.404537] [sprd_tcpm_log] Setting voltage/current limit 5000 mV 0 mA
<6>[   11.404795][T334@C4] [114 / 118] [   11.404540] [sprd_tcpm_log] state change SNK_DISCOVERY -> SNK_WAIT_CAPABILITIES
<6>[   11.404812][T334@C4] [115 / 118] [   11.404543] [sprd_pd_log] sc27xx_pd_reset:line784: clear_tx_id = 0
<6>[   11.404829][T334@C4] [116 / 118] [   11.404546] [sprd_pd_log] sc27xx_pd_rx_flush:line761: start rx flush
<6>[   11.404848][T334@C4] [117 / 118] [   11.404644] [sprd_pd_log] sc27xx_pd_rx_flush:line764: rx flush done
<6>[   11.404946][T334@C4] [118 / 119] [   11.404907] [sprd_pd_log] set rx: on = 1, sts1 = 0x2000
<6>[   11.405193][T335@C7] sc27xx-typec-pd 64200000.spi:pmic@0:pd@e00: pd := on
<6>[   11.405215][T334@C4] [119 / 121] [   11.405178] [sprd_pd_log] set rx: pd := on
<6>[   11.405234][T334@C4] [120 / 121] [   11.405209] [sprd_tcpm_log] pending state change SNK_WAIT_CAPABILITIES -> SNK_READY @ 460 ms
<6>[   11.865508][T335@C7] sc27xx-typec 64200000.spi:pmic@0:typec@380: sc27xx_typec_pr_swap_no_chk_detach, on = 0
<6>[   11.865758][T334@C4] [121 / 123] [   11.865480] [sprd_tcpm_log] state change SNK_WAIT_CAPABILITIES -> SNK_READY [delayed 460 ms]
<6>[   11.865783][T334@C4] [122 / 123] [   11.865501] [sprd_tcpm_log] sprd_tcpm_typec_pr_swap_no_chk_detach, cur state: SNK_READY, on: 0
<6>[   13.644443][T24@C0] android_work: sent uevent USB_STATE=CONFIGURED
<3>[   16.194217][T24@C0] |__c2e     mmc0:   33    0    0    0    0    0    0    0    0    0    0    0    0    0
<3>[   16.194261][T24@C0] |__d2e     mmc0:   18    0    0    3    0    0    0    0    0    0    0    0    0    0
<3>[   16.194284][T24@C0] |__blocks  mmc0:    0    0    0    0   14    0    6    1    0    0    0    0    0    0
<3>[   16.194308][T24@C0] |__speed   mmc0: r= 0.81M/s, w= 14.83M/s, r_blk= 8, w_blk= 392
<3>[   17.546410][T418@C3] sprd-wlan: sprd_chr_client_thread, CHR: stop wait connect, go exit!
<6>[   17.546586][T418@C3] sprd-wlan: sprd_chr_client_thread, CHR: exit client_thread
<3>[   20.459835][T132@C7] sprd-fgu 64200000.spi:pmic@0:fgu@c00: sc27xx_fgu_get_tempvol_ntc_uv=279957,calib_resistance_vol=-43,vol_adc_mv=280
<6>[   20.461592][T132@C7] sprd-fgu 64200000.spi:pmic@0:fgu@c00: sc27xx_fgu_info : init_clbcnt = 49958517, start_work_clbcnt = 49958208, cur_clbcnt = 49956006, cur_1000ma_adc = 1380, vol_1000mv_adc = 690, calib_resist = 10000
<6>[   20.461606][T132@C7] sprd-fgu 64200000.spi:pmic@0:fgu@c00: init_cap = 1000, init_mah = 0, normal_cap = 1000, data->cc_mah = 0, Tbat = 306, uusoc_vbat = 0, uusoc_mah = 0, track_sts = 1
<6>[   20.461619][T132@C7] sprd-fgu 64200000.spi:pmic@0:fgu@c00: ocv_uv = 4408000, vbatt_mv = 4404, vbat_cur_ma = -43, vbat_avg_mv = 4403, vbat_cur_avg_ma = -44, absolute_charger_mode = 0, full_percent = 0
<6>[   20.461630][T132@C7] sprd-fgu 64200000.spi:pmic@0:fgu@c00: battery soc = 1000, cycle = 15
<6>[   20.714471][T10@C0] charger-manager charger-manager: Charger has been controlled externally, so no need monitoring
<3>[   20.719058][T340@C2] charger-manager charger-manager: Can not to get board temperature, return init_temp=250
<4>[   20.719092][T340@C2] charger-manager charger-manager: failed to get board temperature
<6>[   20.719534][T340@C2] charger-manager charger-manager: vbat: 4404000, vbat_avg: 4403000, OCV: 4408000, ibat: -44000, ibat_avg: -43000, ibus: -22, vbus: 5016000, msoc: 1000, chg_sts: 4, frce_full: 1, chg_lmt_cur: 0, inpt_lmt_cur: 0, chgr_type: 1, Tboard: 250, Tbatt: 306, thm_cur: -22, thm_pwr: 0, is_fchg: 0, fchg_en: 0, tflush: 15, tperiod: 15
<6>[   20.720419][T340@C2] charger-manager charger-manager: new_uisoc = 1000, old_uisoc = 1000, work_cycle = 15s, cap_one_time = 30s
<6>[   20.731903][T87@C2] bq2560x_chg 5-006b: bq2560x_dump_register: [REG_0x00]=0x84  [REG_0x01]=0x1a  [REG_0x02]=0x9e  [REG_0x03]=0xa7  [REG_0x04]=0x90  [REG_0x05]=0x8f  [REG_0x06]=0x65  [REG_0x07]=0x08  [REG_0x08]=0x00  [REG_0x09]=0x00  [REG_0x0a]=0x00  [REG_0x0b]=0x00  
<3>[   21.740091][T180@C0] |__i2s     mmc0:   40    0    2    7    0    0    0    0    0    0    0    0    0    0
<3>[   21.740115][T180@C0] |__i2e     mmc0:   35    0    2   12    0    0    0    0    0    0    0    0    0    0
<3>[   21.740125][T180@C0] |__swcq    mmc0: cmdq_mode= 0, recovery_cnt= 0, qcnt= 1, cmdq_cnt= 0
<3>[   21.740132][T180@C0] |__swcq    mmc0: cmd resp err cnt 0, 0, 0, 0, 0, 0, 0
<6>[   21.744977][    C0] |_mmcblk0    total complete 60 requests
<6>[   21.744992][    C0] |_mmcblk0    R complete:      8 request       64 sectors
<6>[   21.744998][    C0] |_mmcblk0    R i2i[  8- 8192ms]:    8    0    0    0    0    0    0    0    0    0    0    0
<6>[   21.745009][    C0] |_mmcblk0    R i2c[  8- 8192ms]:    8    0    0    0    0    0    0    0    0    0    0    0
<6>[   21.745018][    C0] |_mmcblk0    W complete:     45 request      736 sectors
<6>[   21.745022][    C0] |_mmcblk0    W i2i[  8- 8192ms]:   45    0    0    0    0    0    0    0    0    0    0    0
<6>[   21.745031][    C0] |_mmcblk0    W i2c[  8- 8192ms]:   45    0    0    0    0    0    0    0    0    0    0    0
<6>[   21.745043][    C0] |_mmcblk0    F complete:      7 request        0 sectors
<6>[   21.745047][    C0] |_mmcblk0    F i2i[  8- 8192ms]:    7    0    0    0    0    0    0    0    0    0    0    0
<6>[   21.745055][    C0] |_mmcblk0    F i2c[  8- 8192ms]:    7    0    0    0    0    0    0    0    0    0    0    0
<3>[   21.745281][T24@C0] |__c2e     mmc0:   38    0    0    0    0    0    0    0    0    0    0    0    0    0
<3>[   21.745310][T24@C0] |__d2e     mmc0:   26    0    0    2    0    0    0    0    0    0    0    0    0    0
<3>[   21.745333][T24@C0] |__blocks  mmc0:    0    0    0    0   21    3    3    1    0    0    0    0    0    0
<3>[   21.745356][T24@C0] |__speed   mmc0: r= 31.0M/s, w= 12.14M/s, r_blk= 56, w_blk= 344
<3>[   27.888553][T24@C0] |__c2e     mmc0:   24    0    0    0    0    0    0    0    0    0    0    0    0    0
<3>[   27.888602][T24@C0] |__d2e     mmc0:   12    0    0    2    0    0    0    0    0    0    0    0    0    0
<3>[   27.888630][T24@C0] |__blocks  mmc0:    0    0    0    0    8    3    3    0    0    0    0    0    0    0
<3>[   27.888655][T24@C0] |__speed   mmc0: r= 0.0M/s, w= 10.7M/s, r_blk= 0, w_blk= 240
<3>[   31.722704][T153@C0] [SPRD_PDBG] #---------PDBG LIGHT SLEEP START---------#
<3>[   31.722758][T153@C0] [SPRD_PDBG] Pending Wakeup Sources: musb-hdrc.1.auto 
<3>[   31.722797][T153@C0] [SPRD_PDBG] [SLP_STATE] deep: 0xfffffffffffffff0, light: 0xfffffffffffffff4
<3>[   31.722831][T153@C0] [SPRD_PDBG] [EB_INFO  ] ap1: 0xfffffffffffffffe, ap2: 0xffffffffffffffff, aon1: 0xffffffe40003ef0a, aon2: 0xffffffff1200007c
<3>[   31.722857][T153@C0] [SPRD_PDBG] [PD_INFO  ] 0xfffffffffff27de0
<3>[   31.722886][T153@C0] [SPRD_PDBG] [DEEP_CNT ]     0,     0,     5,     1,     1,     2,     9, 
<3>[   31.722910][T153@C0] [SPRD_PDBG] [LIGHT_CNT]    31, 
<3>[   31.722933][T153@C0] [SPRD_PDBG] #---------PDBG LIGHT SLEEP END-----------#
<6>[   31.723533][T114@C6] vddldo2: disabling
<3>[   33.771542][T269@C5] |__i2s     mmc0:   15    0    2    3    0    0    0    0    0    0    0    0    0    0
<3>[   33.771565][T269@C5] |__i2e     mmc0:   11    0    1    8    0    0    0    0    0    0    0    0    0    0
<3>[   33.771575][T269@C5] |__swcq    mmc0: cmdq_mode= 0, recovery_cnt= 0, qcnt= 1, cmdq_cnt= 0
<3>[   33.771581][T269@C5] |__swcq    mmc0: cmd resp err cnt 0, 0, 0, 0, 0, 0, 0
<3>[   33.776430][T24@C0] |__c2e     mmc0:   12    0    0    0    0    0    0    0    0    0    0    0    0    0
<3>[   33.776467][T24@C0] |__d2e     mmc0:    4    0    0    2    0    0    0    0    0    0    0    0    0    0
<3>[   33.776492][T24@C0] |__blocks  mmc0:    0    0    0    0    4    0    2    0    0    0    0    0    0    0
<3>[   33.776520][T24@C0] |__speed   mmc0: r= 0.0M/s, w= 5.2M/s, r_blk= 0, w_blk= 104
<6>[   33.776593][    C5] |_mmcblk0    total complete 28 requests
<6>[   33.776605][    C5] |_mmcblk0    W complete:     23 request      344 sectors
<6>[   33.776611][    C5] |_mmcblk0    W i2i[  8- 8192ms]:   23    0    0    0    0    0    0    0    0    0    0    0
<6>[   33.776622][    C5] |_mmcblk0    W i2c[  8- 8192ms]:   23    0    0    0    0    0    0    0    0    0    0    0
<6>[   33.776632][    C5] |_mmcblk0    F complete:      5 request        0 sectors
<6>[   33.776637][    C5] |_mmcblk0    F i2i[  8- 8192ms]:    5    0    0    0    0    0    0    0    0    0    0    0
<6>[   33.776646][    C5] |_mmcblk0    F i2c[  8- 8192ms]:    5    0    0    0    0    0    0    0    0    0    0    0
<3>[   35.563568][T132@C7] sprd-fgu 64200000.spi:pmic@0:fgu@c00: sc27xx_fgu_get_tempvol_ntc_uv=281957,calib_resistance_vol=-43,vol_adc_mv=282
<6>[   35.565378][T132@C7] sprd-fgu 64200000.spi:pmic@0:fgu@c00: sc27xx_fgu_info : init_clbcnt = 49958517, start_work_clbcnt = 49958208, cur_clbcnt = 49954126, cur_1000ma_adc = 1380, vol_1000mv_adc = 690, calib_resist = 10000
<6>[   35.565392][T132@C7] sprd-fgu 64200000.spi:pmic@0:fgu@c00: init_cap = 1000, init_mah = 0, normal_cap = 1000, data->cc_mah = 0, Tbat = 305, uusoc_vbat = 0, uusoc_mah = 0, track_sts = 1
<6>[   35.565404][T132@C7] sprd-fgu 64200000.spi:pmic@0:fgu@c00: ocv_uv = 4407000, vbatt_mv = 4403, vbat_cur_ma = -43, vbat_avg_mv = 4403, vbat_cur_avg_ma = -44, absolute_charger_mode = 0, full_percent = 0
<6>[   35.565416][T132@C7] sprd-fgu 64200000.spi:pmic@0:fgu@c00: battery soc = 1000, cycle = 15
<6>[   35.818713][T103@C1] charger-manager charger-manager: Charger has been controlled externally, so no need monitoring
<3>[   35.825164][T340@C2] charger-manager charger-manager: Can not to get board temperature, return init_temp=250
<4>[   35.825198][T340@C2] charger-manager charger-manager: failed to get board temperature
<6>[   35.825662][T340@C2] charger-manager charger-manager: vbat: 4403000, vbat_avg: 4403000, OCV: 4407000, ibat: -43000, ibat_avg: -44000, ibus: -22, vbus: 4963000, msoc: 1000, chg_sts: 4, frce_full: 1, chg_lmt_cur: 0, inpt_lmt_cur: 0, chgr_type: 1, Tboard: 250, Tbatt: 305, thm_cur: -22, thm_pwr: 0, is_fchg: 0, fchg_en: 0, tflush: 15, tperiod: 15
<6>[   35.827148][T340@C2] charger-manager charger-manager: new_uisoc = 1000, old_uisoc = 1000, work_cycle = 15s, cap_one_time = 30s
<6>[   35.838053][T87@C2] bq2560x_chg 5-006b: bq2560x_dump_register: [REG_0x00]=0x84  [REG_0x01]=0x1a  [REG_0x02]=0x9e  [REG_0x03]=0xa7  [REG_0x04]=0x90  [REG_0x05]=0x8f  [REG_0x06]=0x65  [REG_0x07]=0x08  [REG_0x08]=0x00  [REG_0x09]=0x00  [REG_0x0a]=0x00  [REG_0x0b]=0x00  
<3>[   39.920596][T24@C0] |__c2e     mmc0:   18    0    0    0    0    0    0    0    0    0    0    0    0    0
<3>[   39.920639][T24@C0] |__d2e     mmc0:   10    0    0    2    0    0    0    0    0    0    0    0    0    0
<3>[   39.920667][T24@C0] |__blocks  mmc0:    0    0    0    0    8    2    2    0    0    0    0    0    0    0
<3>[   39.920693][T24@C0] |__speed   mmc0: r= 0.0M/s, w= 7.9M/s, r_blk= 0, w_blk= 168
<3>[   45.803786][T435@C1] |__i2s     mmc0:   18    0    3    4    0    0    0    0    0    0    0    0    0    0
<3>[   45.803816][T435@C1] |__i2e     mmc0:   14    0    3    8    0    0    0    0    0    0    0    0    0    0
<3>[   45.803826][T435@C1] |__swcq    mmc0: cmdq_mode= 0, recovery_cnt= 0, qcnt= 1, cmdq_cnt= 0
<3>[   45.803832][T435@C1] |__swcq    mmc0: cmd resp err cnt 0, 0, 0, 0, 0, 0, 0
<3>[   45.808641][T24@C0] |__c2e     mmc0:   21    0    0    0    0    0    0    0    0    0    0    0    0    0
<3>[   45.808682][T24@C0] |__d2e     mmc0:   11    0    0    2    0    0    0    0    0    0    0    0    0    0
<3>[   45.808707][T24@C0] |__blocks  mmc0:    0    0    0    0    8    4    1    0    0    0    0    0    0    0
<3>[   45.808733][T24@C0] |__speed   mmc0: r= 0.0M/s, w= 7.75M/s, r_blk= 0, w_blk= 184
<6>[   45.808848][    C1] |_mmcblk0    total complete 31 requests
<6>[   45.808860][    C1] |_mmcblk0    W complete:     27 request      352 sectors
<6>[   45.808866][    C1] |_mmcblk0    W i2i[  8- 8192ms]:   27    0    0    0    0    0    0    0    0    0    0    0
<6>[   45.808877][    C1] |_mmcblk0    W i2c[  8- 8192ms]:   27    0    0    0    0    0    0    0    0    0    0    0
<6>[   45.808887][    C1] |_mmcblk0    F complete:      4 request        0 sectors
<6>[   45.808892][    C1] |_mmcblk0    F i2i[  8- 8192ms]:    4    0    0    0    0    0    0    0    0    0    0    0
<6>[   45.808902][    C1] |_mmcblk0    F i2c[  8- 8192ms]:    4    0    0    0    0    0    0    0    0    0    0    0
<3>[   50.667669][T132@C7] sprd-fgu 64200000.spi:pmic@0:fgu@c00: sc27xx_fgu_get_tempvol_ntc_uv=281957,calib_resistance_vol=-43,vol_adc_mv=282
<6>[   50.669537][T132@C7] sprd-fgu 64200000.spi:pmic@0:fgu@c00: sc27xx_fgu_info : init_clbcnt = 49958517, start_work_clbcnt = 49958208, cur_clbcnt = 49952334, cur_1000ma_adc = 1380, vol_1000mv_adc = 690, calib_resist = 10000
<6>[   50.669552][T132@C7] sprd-fgu 64200000.spi:pmic@0:fgu@c00: init_cap = 1000, init_mah = 0, normal_cap = 1000, data->cc_mah = 0, Tbat = 304, uusoc_vbat = 0, uusoc_mah = 0, track_sts = 1
<6>[   50.669565][T132@C7] sprd-fgu 64200000.spi:pmic@0:fgu@c00: ocv_uv = 4408000, vbatt_mv = 4404, vbat_cur_ma = -43, vbat_avg_mv = 4404, vbat_cur_avg_ma = -43, absolute_charger_mode = 0, full_percent = 0
<6>[   50.669576][T132@C7] sprd-fgu 64200000.spi:pmic@0:fgu@c00: battery soc = 1000, cycle = 15
<6>[   50.922630][T103@C2] charger-manager charger-manager: Charger has been controlled externally, so no need monitoring
<3>[   50.928418][T24@C0] |__c2e     mmc0:    1    0    0    0    0    0    0    0    0    0    0    0    0    0
<3>[   50.928451][T24@C0] |__d2e     mmc0:    0    0    0    1    0    0    0    0    0    0    0    0    0    0
<3>[   50.928475][T24@C0] |__blocks  mmc0:    0    0    0    0    1    0    0    0    0    0    0    0    0    0
<3>[   50.928497][T24@C0] |__speed   mmc0: r= 0.0M/s, w= 0.87M/s, r_blk= 0, w_blk= 8
<3>[   50.930216][T340@C2] charger-manager charger-manager: Can not to get board temperature, return init_temp=250
<4>[   50.930242][T340@C2] charger-manager charger-manager: failed to get board temperature
<6>[   50.930662][T340@C2] charger-manager charger-manager: vbat: 4404000, vbat_avg: 4404000, OCV: 4408000, ibat: -43000, ibat_avg: -43000, ibus: -22, vbus: 4976000, msoc: 1000, chg_sts: 4, frce_full: 1, chg_lmt_cur: 0, inpt_lmt_cur: 0, chgr_type: 1, Tboard: 250, Tbatt: 304, thm_cur: -22, thm_pwr: 0, is_fchg: 0, fchg_en: 0, tflush: 15, tperiod: 15
<6>[   50.932409][T340@C2] charger-manager charger-manager: new_uisoc = 1000, old_uisoc = 1000, work_cycle = 15s, cap_one_time = 30s
<6>[   50.944309][T87@C2] bq2560x_chg 5-006b: bq2560x_dump_register: [REG_0x00]=0x84  [REG_0x01]=0x1a  [REG_0x02]=0x9e  [REG_0x03]=0xa7  [REG_0x04]=0x90  [REG_0x05]=0x8f  [REG_0x06]=0x65  [REG_0x07]=0x08  [REG_0x08]=0x00  [REG_0x09]=0x00  [REG_0x0a]=0x00  [REG_0x0b]=0x00  
<6>[   57.753985][    C2] u32 dl_rx_packet_count= 0; u64 dl_rx_bytes= 0; u32 dl_sent_packet_count= 0; u64 dl_sent_bytes= 0; u32 dl_sent_failed_packet_count= 0; u64 min_time_per_xfer= 18446744073709551615(ns); u64 max_time_per_xfer= 0(ns); u64 max_cb_time_per_xfer= 0(ns);
<3>[   57.755199][T277@C2] |__i2s     mmc0:    9    0    1    2    0    0    0    0    0    0    0    0    0    0
<3>[   57.755219][T277@C2] |__i2e     mmc0:    6    0    1    5    0    0    0    0    0    0    0    0    0    0
<3>[   57.755228][T277@C2] |__swcq    mmc0: cmdq_mode= 0, recovery_cnt= 0, qcnt= 1, cmdq_cnt= 0
<3>[   57.755233][T277@C2] |__swcq    mmc0: cmd resp err cnt 0, 0, 0, 0, 0, 0, 0
<3>[   57.759925][T24@C0] |__c2e     mmc0:   17    0    0    0    0    0    0    0    0    0    0    0    0    0
<3>[   57.759959][T24@C0] |__d2e     mmc0:    9    0    0    2    0    0    0    0    0    0    0    0    0    0
<3>[   57.759986][T24@C0] |__blocks  mmc0:    0    0    0    0    7    1    3    0    0    0    0    0    0    0
<3>[   57.760012][T24@C0] |__speed   mmc0: r= 0.85M/s, w= 15.89M/s, r_blk= 8, w_blk= 184
<6>[   57.760129][    C2] |_mmcblk0    total complete 15 requests
<6>[   57.760141][    C2] |_mmcblk0    R complete:      1 request        8 sectors
<6>[   57.760146][    C2] |_mmcblk0    R i2i[  8- 8192ms]:    1    0    0    0    0    0    0    0    0    0    0    0
<6>[   57.760156][    C2] |_mmcblk0    R i2c[  8- 8192ms]:    1    0    0    0    0    0    0    0    0    0    0    0
<6>[   57.760165][    C2] |_mmcblk0    W complete:     12 request      192 sectors
<6>[   57.760170][    C2] |_mmcblk0    W i2i[  8- 8192ms]:   12    0    0    0    0    0    0    0    0    0    0    0
<6>[   57.760178][    C2] |_mmcblk0    W i2c[  8- 8192ms]:   12    0    0    0    0    0    0    0    0    0    0    0
<6>[   57.760188][    C2] |_mmcblk0    F complete:      2 request        0 sectors
<6>[   57.760192][    C2] |_mmcblk0    F i2i[  8- 8192ms]:    2    0    0    0    0    0    0    0    0    0    0    0
<6>[   57.760201][    C2] |_mmcblk0    F i2c[  8- 8192ms]:    2    0    0    0    0    0    0    0    0    0    0    0
<6>[   61.259728][T310@C1] sprd-apcpu-dvfs: policy[0] disables boost it is 60 seconds after boot up
<6>[   62.442515][    C7] sprd-mpm: pmsys-wt is awake, awake_cnt = 5
<6>[   62.442515][    C7] smsg-6-7-rx is awake, awake_cnt = 2
<6>[   62.442515][    C7] smsg-6-6-rx is awake, awake_cnt = 3
<6>[   62.442515][    C7] sbuf-6-6-0-rx is awake, awake_cnt = 1
<6>[   62.442515][    C7] smsg-6-5-rx is awake, awake_cnt = 2
<6>[   62.442515][    C7] smsg-6-4-rx is awake, awake_cnt = 2
<6>[   62.442515][    C7] 
<6>[   62.442573][    C7] sprd-mpm: modem-wt is awake, awake_cnt = 12
<6>[   62.442573][    C7] smsg-5-5-rx is awake, awake_cnt = 2
<6>[   62.442573][    C7] smsg-5-21-rx is awake, awake_cnt = 2
<6>[   62.442573][    C7] smsg-5-6-rx is awake, awake_cnt = 3
<6>[   62.442573][    C7] sbuf-5-6-0-rx is awake, awake_cnt = 1
<6>[   62.442573][    C7] smsg-5-4-rx is awake, awake_cnt = 5
<6>[   62.442573][    C7] sbuf-5-4-8-rx is awake, awake_cnt = 1
<6>[   62.442573][    C7] 
<3>[   62.442582][T153@C0] [SPRD_PDBG] #---------PDBG LIGHT SLEEP START---------#
<6>[   62.442591][    C7] sprd-mpm: smsg-3-5-rx is awake, awake_cnt = 32
<6>[   62.442591][    C7] sbuf-3-5-0-rx is awake, awake_cnt = 28
<6>[   62.442591][    C7] smsg-3-12-rx is awake, awake_cnt = 4
<6>[   62.442591][    C7] smsg-3-11-rx is awake, awake_cnt = 4
<6>[   62.442591][    C7] smsg-3-4-rx is awake, awake_cnt = 10
<6>[   62.442591][    C7] sbuf-3-4-5-rx is awake, awake_cnt = 6
<6>[   62.442591][    C7] 
<6>[   62.442605][    C7] start rotation feature
<3>[   62.442651][T153@C0] [SPRD_PDBG] Pending Wakeup Sources: musb-hdrc.1.auto 
<3>[   62.442716][T153@C0] [SPRD_PDBG] [SLP_STATE] deep: 0xfffffffffffffff0, light: 0xfffffffffffffff4
<3>[   62.442782][T153@C0] [SPRD_PDBG] [EB_INFO  ] ap1: 0xfffffffffffffffe, ap2: 0xffffffffffffffff, aon1: 0xffffffe40003ef0a, aon2: 0xffffffff1200007c
<3>[   62.442836][T153@C0] [SPRD_PDBG] [PD_INFO  ] 0xfffffffffff27c20
<3>[   62.442893][T153@C0] [SPRD_PDBG] [DEEP_CNT ]     0,     0,     5,     1,     1,     2,     9, 
<3>[   62.442944][T153@C0] [SPRD_PDBG] [LIGHT_CNT]    73, 
<3>[   62.442995][T153@C0] [SPRD_PDBG] #---------PDBG LIGHT SLEEP END-----------#
<6>[   62.954418][    C2] u32 dl_rx_packet_count= 0; u64 dl_rx_bytes= 0; u32 dl_sent_packet_count= 0; u64 dl_sent_bytes= 0; u32 dl_sent_failed_packet_count= 0; u64 min_time_per_xfer= 18446744073709551615(ns); u64 max_time_per_xfer= 0(ns); u64 max_cb_time_per_xfer= 0(ns);
<3>[   63.986478][T24@C0] |__c2e     mmc0:  154    0    0    0    0    0    0    0    0    0    0    0    0    0
<3>[   63.986558][T24@C0] |__d2e     mmc0:  146    0    0    2    0    0    0    0    0    0    0    0    0    0
<3>[   63.986616][T24@C0] |__blocks  mmc0:    0    0    0    0   46   63   18   17    4    0    0    0    0    0
<3>[   63.986675][T24@C0] |__speed   mmc0: r= 69.85M/s, w= 12.35M/s, r_blk= 3792, w_blk= 296
<6>[   65.596934][T129@C0] sc27xx-typec 64200000.spi:pmic@0:typec@380: sc27xx_typec_interrupt enter line 664
<6>[   65.597156][T129@C0] sc27xx-typec 64200000.spi:pmic@0:typec@380: sc27xx_typec_set_rp_rd cc:0!
<6>[   65.597178][T129@C0] sc27xx-typec 64200000.spi:pmic@0:typec@380: set cc_2 hw
<6>[   65.597291][T129@C0] sc27xx-typec 64200000.spi:pmic@0:typec@380: sc27xx_typec_set_rp_level cc:3!
<6>[   65.597310][T129@C0] sc27xx-typec 64200000.spi:pmic@0:typec@380: rp level: 80UA
<6>[   65.597424][T129@C0] sc27xx-typec 64200000.spi:pmic@0:typec@380: sc27xx_disconnect_set_status_use_pdhubc2c dr_mode:1  pr_mode:3!
<6>[   65.597474][T129@C0] sc27xx-typec-pd 64200000.spi:pmic@0:pd@e00: typec in or out, pd attached = 1
<6>[   65.597730][T129@C0] sc27xx-typec-pd 64200000.spi:pmic@0:pd@e00: sc27xx_pd_extcon_notify_dp:hpd_status = 3
<6>[   65.597794][T129@C0] sc27xx-typec-pd 64200000.spi:pmic@0:pd@e00: sc27xx pd power role swap false
<6>[   65.597842][T129@C0] charger-manager charger-manager: is_sink = 1, state = 0
<6>[   65.597988][T334@C4] [123 / 124] [   65.597757] [sprd_pd_log] sc27xx pd power role swap false
<6>[   65.598114][T129@C0] musb-sprd 64900000.usb: vbus:0 event received
<6>[   65.598277][T103@C2] musb-sprd 64900000.usb: ext event: id 2, vbus 0, b_susp 0, a_recover 0, b_data 1
<6>[   65.598349][T103@C2] musb-sprd 64900000.usb: sm_work: peripheral state
<6>[   65.598384][T325@C7] sprd-hsphy 64570000.hsphy: bc1p2:usb_charger_absent
<6>[   65.598387][T103@C2] musb-sprd 64900000.usb: musb_sprd_otg_start_peripheral: turn off gadget musb-hdrc
<6>[   65.598426][T311@C5] sprd-apcpu-dvfs: policy[6] disables boost it is 60 seconds after boot up
<6>[   65.598511][T24@C0] sc27xx-typec-pd 64200000.spi:pmic@0:pd@e00: check vbus and cc, ktime = 65584 ms
<6>[   65.598519][T87@C2] musb-hdrc musb-hdrc.1.auto: gadget D+ pullup off
<6>[   65.598585][T334@C4] [124 / 125] [   65.598413] [sprd_pd_log] check vbus and cc, ktime = 65584 ms
<6>[   65.599144][T103@C2] [call_sprd_usbphy_event_notifiers]id(0),val(0)
<6>[   65.599187][T103@C2] __func__:sprd_hsphy_typec_notifier, event false
<6>[   65.599223][T103@C2] switch dp/dm to other
<6>[   65.599746][T334@C4] [125 / 128] [   65.599386] [sprd_pd_log] typec status = 0
<6>[   65.599766][T334@C4] [126 / 128] [   65.599510] [sprd_pd_log] typec plug out
<6>[   65.599783][T334@C4] [127 / 128] [   65.599515] [sprd_pd_log] rp_level = 4, rp = 3
<6>[   65.600392][T103@C2] musb-sprd 64900000.usb: sm_work: runtime_suspending state
<6>[   65.600413][T103@C2] musb-sprd 64900000.usb: waiting glue suspended
<6>[   65.601176][T181@C0] charger-manager charger-manager: sprd_vchg: charger type = 0, charger online = 0
<6>[   65.601532][T181@C0] charger-manager charger-manager: set wirless type = 0
<6>[   65.602795][T335@C7] sc27xx-typec-pd 64200000.spi:pmic@0:pd@e00: pd := off
<6>[   65.602817][T335@C7] sc27xx-typec-pd 64200000.spi:pmic@0:pd@e00: vbus is already Off
<4>[   65.602828][T335@C7] sc27xx-typec-pd 64200000.spi:pmic@0:pd@e00: vconn NULL!!!
<6>[   65.603780][ T6@C0] android_work: sent uevent USB_STATE=DISCONNECTED
<6>[   65.608061][T181@C0] charger-manager charger-manager: Charger has been controlled externally, so no need monitoring
<6>[   65.608114][T181@C0] vote_gov: vote_all[disable]
<6>[   65.608163][T181@C0] charger-manager charger-manager: cm_update_charger_type_status:line6524 usb = 0, ac = 0, wireless = 0
<3>[   65.611717][T181@C0] charger-manager charger-manager: Can not to get board temperature, return init_temp=250
<3>[   65.611754][T181@C0] power_supply battery: driver failed to report `temp_ambient' property: -22
<6>[   65.626583][T334@C4] [128 / 145] [   65.599792] [sprd_pd_log] disconnect, use_pdhub_c2c = 1
<6>[   65.626619][T334@C4] [129 / 145] [   65.599803] [sprd_pd_log] sc27xx_pd_reset:line784: clear_tx_id = 1
<6>[   65.626637][T334@C4] [130 / 145] [   65.599806] [sprd_pd_log] sc27xx_pd_rx_flush:line761: start rx flush
<6>[   65.626654][T334@C4] [131 / 145] [   65.599993] [sprd_pd_log] sc27xx_pd_rx_flush:line764: rx flush done
<6>[   65.626673][T334@C4] [132 / 145] [   65.600498] [sprd_pd_log] sc27xx_pd_reset:line799: clear tx id
<6>[   65.626691][T334@C4] [133 / 145] [   65.600658] [sprd_tcpm_log] CC1: 0 -> 0, CC2: 3 -> 0 [state SNK_READY, polarity 1, disconnected]
<6>[   65.626709][T334@C4] [134 / 145] [   65.600666] [sprd_tcpm_log] state change SNK_READY -> SNK_UNATTACHED
<6>[   65.626726][T334@C4] [135 / 145] [   65.600807] [sprd_tcpm_log] sprd_tcpm_reset_port:line3062
<6>[   65.626744][T334@C4] [136 / 145] [   65.601105] [sprd_pd_log] sc27xx_pd_reset:line784: clear_tx_id = 0
<6>[   65.626761][T334@C4] [137 / 145] [   65.601108] [sprd_pd_log] sc27xx_pd_rx_flush:line761: start rx flush
<6>[   65.626779][T334@C4] [138 / 145] [   65.601325] [sprd_pd_log] sc27xx_pd_rx_flush:line764: rx flush done
<6>[   65.626796][T334@C4] [139 / 145] [   65.602333] [sprd_pd_log] set rx: on = 0, sts1 = 0x2000
<6>[   65.626813][T334@C4] [140 / 145] [   65.602786] [sprd_pd_log] set rx: pd := off
<6>[   65.626830][T334@C4] [141 / 145] [   65.602810] [sprd_pd_log] vbus is already Off
<6>[   65.626830][T334@C4] 
<6>[   65.626847][T334@C4] [142 / 145] [   65.603244] [sprd_tcpm_log] Start toggling
<6>[   65.626865][T334@C4] [143 / 145] [   65.606906] [sprd_pd_log] vbus status = 0x200, vbus_present = 0
<6>[   65.626882][T334@C4] [144 / 145] [   65.607155] [sprd_tcpm_log] VBUS off
<14>[   65.635795][T214@C1] init: Received sys.powerctl='shutdown' from pid: 354 (/vendor/bin/engpc)
<14>[   65.636187][ T1@C6] init: Got shutdown_command 'shutdown' Calling HandlePowerctlMessage()
<14>[   65.636261][ T1@C6] init: Clear action queue and start shutdown trigger
<14>[   65.636567][ T1@C6] init: Entering shutdown mode
<14>[   65.637600][ T1@C6] init: processing action (shutdown_done) from (<Builtin Action>:0)
<14>[   65.637623][ T1@C6] init: Reboot start, reason: shutdown, reboot_target: 
<14>[   65.637650][ T1@C6] init: Shutdown timeout: 6000 ms
<14>[   65.637669][ T1@C6] init: Create reboot monitor thread.
<14>[   65.638811][T520@C5] init: shutdown_timeout_timespec.tv_sec: 371
<11>[   65.639668][ T1@C6] init: Could not open '/metadata/bootstat/persist.sys.boot.reason' to persist reboot reason: No such file or directory
<3>[   65.640303][ T1@C6] shutdown_detect_check: shutdown_detect_phase: shutdown current phase init
<14>[   65.642014][ T1@C6] init: service 'ueventd' requested start, but it is already running (flags: 2084)
<14>[   65.642137][ T1@C6] init: starting service 'watchdogd'...
<14>[   65.656541][ T1@C6] init: ... started service 'watchdogd' has pid 523
<14>[   65.657120][ T1@C6] init: starting service 'blank_screen'...
<14>[   65.669982][ T1@C6] init: ... started service 'blank_screen' has pid 524
<14>[   65.670300][ T1@C6] init: service 'servicemanager' requested start, but it is already running (flags: 2084)
<14>[   65.670822][ T1@C6] init: starting service 'vold'...
<14>[   65.683162][ T1@C6] init: ... started service 'vold' has pid 525
<14>[   65.683370][ T1@C6] init: service 'hwservicemanager' requested start, but it is already running (flags: 2212)
<14>[   65.683960][ T1@C6] init: starting service 'vendor.sprd.hardware.lights-service'...
<14>[   65.693863][T523@C7] watchdogd: watchdogd started (interval 10, margin 30)!
<11>[   65.694177][T523@C7] watchdogd: Failed to open /dev/watchdog: No such file or directory
<14>[   65.696355][ T1@C7] init: ... started service 'vendor.sprd.hardware.lights-service' has pid 526
<14>[   65.696684][ T1@C7] init: service 'vndservicemanager' requested start, but it is already running (flags: 2052)
<14>[   65.696830][ T1@C7] init: service 'blank_screen' requested start, but it is already running (flags: 2054)
<14>[   65.696857][ T1@C7] init: Stopping 220 services by sending SIGTERM
<14>[   65.696941][ T1@C7] init: Sending signal 15 to service 'srmi_proxyd' (pid 515) process group...
<12>[   65.697740][ T1@C7] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_515 after 0 ms
<11>[   65.697859][ T1@C7] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_515: Device or resource busy
<14>[   65.698826][ T1@C7] init: Sending signal 15 to service 'poweronlog' (pid 398) process group...
<12>[   65.699401][ T1@C7] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_398 after 0 ms
<11>[   65.699484][ T1@C7] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_398: Device or resource busy
<14>[   65.701905][ T1@C7] init: Sending signal 15 to service 'ylog' (pid 397) process group...
<12>[   65.703420][ T1@C7] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_397 after 0 ms
<11>[   65.703519][ T1@C7] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_397: Device or resource busy
<14>[   65.705657][ T1@C7] init: Sending signal 15 to service 'vendor.teensproxy' (pid 395) process group...
<12>[   65.706214][ T1@C7] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_395 after 0 ms
<14>[   65.709428][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_395
<14>[   65.715988][ T1@C6] init: Sending signal 15 to service 'nfc_hal_service.tms.aidl' (pid 394) process group...
<12>[   65.716524][ T1@C6] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1027/pid_394 after 0 ms
<11>[   65.716607][ T1@C6] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1027/pid_394: Device or resource busy
<14>[   65.717366][ T1@C6] init: Sending signal 15 to service 'vendor.identity-default' (pid 385) process group...
<12>[   65.717785][ T1@C6] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_385 after 0 ms
<11>[   65.717863][ T1@C6] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_385: Device or resource busy
<14>[   65.718582][ T1@C6] init: Sending signal 15 to service 'vendor.fingerprint-default' (pid 384) process group...
<12>[   65.719024][ T1@C6] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_384 after 0 ms
<11>[   65.719099][ T1@C6] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_384: Device or resource busy
<14>[   65.719774][ T1@C6] init: Sending signal 15 to service 'vendor.face-default' (pid 383) process group...
<12>[   65.720184][ T1@C6] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_383 after 0 ms
<11>[   65.720258][ T1@C6] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_383: Device or resource busy
<14>[   65.720821][ T1@C7] init: Sending signal 15 to service 'vendor.cp_diskserver' (pid 381) process group...
<12>[   65.721276][ T1@C7] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_381 after 0 ms
<11>[   65.721355][ T1@C7] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_381: Device or resource busy
<14>[   65.724384][ T1@C6] init: Sending signal 15 to service 'traced' (pid 368) process group...
<12>[   65.724890][ T1@C6] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_9999/pid_368 after 0 ms
<11>[   65.724973][ T1@C6] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_9999/pid_368: Device or resource busy
<14>[   65.725705][ T1@C6] init: Sending signal 15 to service 'traced_probes' (pid 366) process group...
<12>[   65.726198][ T1@C6] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_9999/pid_366 after 0 ms
<11>[   65.726277][ T1@C6] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_9999/pid_366: Device or resource busy
<14>[   65.726968][ T1@C6] init: Sending signal 15 to service 'drm' (pid 363) process group...
<12>[   65.727411][ T1@C6] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1019/pid_363 after 0 ms
<11>[   65.727486][ T1@C6] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1019/pid_363: Device or resource busy
<14>[   65.728501][ T1@C6] init: Sending signal 15 to service 'vendor.keymint-unisoc-2.0' (pid 359) process group...
<12>[   65.728899][ T1@C6] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_359 after 0 ms
<11>[   65.728973][ T1@C6] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_359: Device or resource busy
<14>[   65.730555][T291@C4] servicemanager: Caller(pid=524,uid=1000,sid=u:r:blank_screen:s0) Found android.hardware.light.ILights/default in device VINTF manifest.
<14>[   65.731431][ T1@C7] init: Sending signal 15 to service 'slogmodem' (pid 358) process group...
<12>[   65.731992][ T1@C7] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_358 after 0 ms
<11>[   65.732076][ T1@C7] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_358: Device or resource busy
<14>[   65.732986][ T1@C7] init: Sending signal 15 to service 'system_suspend' (pid 357) process group...
<12>[   65.733817][ T1@C7] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_357 after 0 ms
<11>[   65.734159][ T1@C7] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_357: Device or resource busy
<14>[   65.735357][ T1@C1] init: Sending signal 15 to service 'vendor.refnotify' (pid 356) process group...
<14>[   65.735958][T291@C2] servicemanager: Caller(pid=524,uid=1000,sid=u:r:blank_screen:s0) Since 'android.hardware.light.ILights/default' could not be found trying to start it as a lazy AIDL service. (if it's not configured to be a lazy service, it may be stuck starting or still starting).
<12>[   65.736334][ T1@C1] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_356 after 0 ms
<11>[   65.736502][ T1@C1] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_356: Device or resource busy
<14>[   65.737380][ T1@C6] init: Sending signal 15 to service 'vendor.modem_control' (pid 355) process group...
<12>[   65.737859][ T1@C6] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_355 after 0 ms
<11>[   65.737990][ T1@C6] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_355: Device or resource busy
<14>[   65.738957][ T1@C6] init: Sending signal 15 to service 'vendor.engpcclientlte' (pid 354) process group...
<12>[   65.739435][ T1@C6] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_354 after 0 ms
<11>[   65.739533][ T1@C6] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_354: Device or resource busy
<11>[   65.739799][T215@C4] init: Unable to set property 'ctl.interface_start' from uid:1000 gid:1000 pid:291: Received control message after shutdown, ignoring
<14>[   65.741392][T527@C1] servicemanager: Caller(pid=524,uid=1000,sid=u:r:blank_screen:s0) Tried to start aidl service android.hardware.light.ILights/default as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
<14>[   65.741781][ T1@C6] init: Sending signal 15 to service 'vendor.tsupplicant-cali' (pid 353) process group...
<12>[   65.742287][ T1@C6] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_353 after 0 ms
<11>[   65.742369][ T1@C6] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_353: Device or resource busy
<14>[   65.743359][ T1@C4] init: Sending signal 15 to service 'vendor.rpmbsvr' (pid 352) process group...
<12>[   65.744209][ T1@C4] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_352 after 0 ms
<11>[   65.744367][ T1@C4] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_352: Device or resource busy
<14>[   65.745414][ T1@C7] init: Sending signal 15 to service 'lmkd' (pid 290) process group...
<12>[   65.746317][ T1@C7] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1069/pid_290 after 0 ms
<11>[   65.746404][ T1@C7] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1069/pid_290: Device or resource busy
<14>[   65.747278][ T1@C7] init: Sending signal 15 to service 'prng_seeder' (pid 279) process group...
<12>[   65.747729][ T1@C7] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1092/pid_279 after 0 ms
<11>[   65.747810][ T1@C7] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1092/pid_279: Device or resource busy
<14>[   65.752005][ T1@C3] init: Service 'prng_seeder' (pid 279) received signal 15
<14>[   65.752061][ T1@C3] init: Sending signal 9 to service 'prng_seeder' (pid 279) process group...
<14>[   65.753505][ T1@C3] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1092/pid_279
<14>[   65.757886][ T1@C6] init: Service 'vendor.rpmbsvr' (pid 352) received signal 15
<14>[   65.757919][ T1@C6] init: Sending signal 9 to service 'vendor.rpmbsvr' (pid 352) process group...
<14>[   65.758729][ T1@C6] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_352
<6>[   65.758962][T367@C3] WCN BASE: z=267386880,major=255,minor = 0
<14>[   65.762187][ T1@C3] init: Service 'vendor.tsupplicant-cali' (pid 353) received signal 15
<14>[   65.762269][ T1@C3] init: Sending signal 9 to service 'vendor.tsupplicant-cali' (pid 353) process group...
<14>[   65.763768][ T1@C3] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_353
<14>[   65.766773][ T1@C0] init: Service 'vendor.engpcclientlte' (pid 354) received signal 15 oneshot service took 59.772999 seconds in background
<14>[   65.766829][ T1@C0] init: Sending signal 9 to service 'vendor.engpcclientlte' (pid 354) process group...
<14>[   65.768808][ T1@C4] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_354
<14>[   65.770097][T291@C2] servicemanager: Caller(pid=526,uid=1000,sid=u:r:hal_light_default:s0) Found android.hardware.light.ILights/default in device VINTF manifest.
<14>[   65.771163][ T1@C6] init: Service 'vendor.modem_control' (pid 355) received signal 15
<14>[   65.771190][ T1@C6] init: Sending signal 9 to service 'vendor.modem_control' (pid 355) process group...
<3>[   65.771268][T132@C7] sprd-fgu 64200000.spi:pmic@0:fgu@c00: sc27xx_fgu_get_tempvol_ntc_uv=283957,calib_resistance_vol=-43,vol_adc_mv=284
<14>[   65.771966][ T1@C6] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_355
<6>[   65.773086][T132@C7] sprd-fgu 64200000.spi:pmic@0:fgu@c00: sc27xx_fgu_info : init_clbcnt = 49958517, start_work_clbcnt = 49958208, cur_clbcnt = 49950313, cur_1000ma_adc = 1380, vol_1000mv_adc = 690, calib_resist = 10000
<6>[   65.773101][T132@C7] sprd-fgu 64200000.spi:pmic@0:fgu@c00: init_cap = 1000, init_mah = 0, normal_cap = 1000, data->cc_mah = 0, Tbat = 304, uusoc_vbat = 0, uusoc_mah = 0, track_sts = 1
<6>[   65.773114][T132@C7] sprd-fgu 64200000.spi:pmic@0:fgu@c00: ocv_uv = 4408000, vbatt_mv = 4404, vbat_cur_ma = -43, vbat_avg_mv = 4403, vbat_cur_avg_ma = -45, absolute_charger_mode = 0, full_percent = 0
<6>[   65.773127][T132@C7] sprd-fgu 64200000.spi:pmic@0:fgu@c00: battery soc = 1000, cycle = 15
<14>[   65.774810][ T1@C7] init: Service 'vendor.refnotify' (pid 356) received signal 15
<14>[   65.774839][ T1@C7] init: Sending signal 9 to service 'vendor.refnotify' (pid 356) process group...
<14>[   65.775618][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_356
<14>[   65.777734][ T1@C7] init: Service 'system_suspend' (pid 357) received signal 15
<14>[   65.777758][ T1@C7] init: Sending signal 9 to service 'system_suspend' (pid 357) process group...
<14>[   65.778522][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_357
<14>[   65.781862][ T1@C3] init: Service 'slogmodem' (pid 358) received signal 15
<14>[   65.781925][ T1@C3] init: Sending signal 9 to service 'slogmodem' (pid 358) process group...
<14>[   65.782969][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_358
<14>[   65.785471][ T1@C7] init: Service 'vendor.keymint-unisoc-2.0' (pid 359) received signal 15
<14>[   65.785502][ T1@C7] init: Sending signal 9 to service 'vendor.keymint-unisoc-2.0' (pid 359) process group...
<14>[   65.786294][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_359
<14>[   65.789051][ T1@C7] init: Service 'drm' (pid 363) received signal 15
<14>[   65.789083][ T1@C7] init: Sending signal 9 to service 'drm' (pid 363) process group...
<14>[   65.789869][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1019/pid_363
<14>[   65.792348][ T1@C7] init: Service 'traced_probes' (pid 366) received signal 15
<14>[   65.792378][ T1@C7] init: Sending signal 9 to service 'traced_probes' (pid 366) process group...
<14>[   65.793163][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_9999/pid_366
<14>[   65.794699][ T1@C7] init: Service 'traced' (pid 368) received signal 15
<14>[   65.794727][ T1@C7] init: Sending signal 9 to service 'traced' (pid 368) process group...
<14>[   65.795460][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_9999/pid_368
<14>[   65.797340][ T1@C7] init: Service 'vendor.cp_diskserver' (pid 381) received signal 15
<14>[   65.797367][ T1@C7] init: Sending signal 9 to service 'vendor.cp_diskserver' (pid 381) process group...
<14>[   65.798172][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_381
<14>[   65.799890][ T1@C7] init: Service 'vendor.face-default' (pid 383) received signal 15
<14>[   65.799920][ T1@C7] init: Sending signal 9 to service 'vendor.face-default' (pid 383) process group...
<14>[   65.800638][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_383
<6>[   65.802050][T10@C2] musb-sprd 64900000.usb: sm_work: runtime_suspending state
<6>[   65.802078][T10@C2] musb-sprd 64900000.usb: waiting glue suspended
<14>[   65.806531][ T1@C6] init: Service 'vendor.fingerprint-default' (pid 384) received signal 15
<14>[   65.806563][ T1@C6] init: Sending signal 9 to service 'vendor.fingerprint-default' (pid 384) process group...
<14>[   65.807340][ T1@C6] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_384
<14>[   65.809565][ T1@C6] init: Service 'vendor.identity-default' (pid 385) received signal 15
<14>[   65.809591][ T1@C6] init: Sending signal 9 to service 'vendor.identity-default' (pid 385) process group...
<14>[   65.810368][ T1@C6] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_385
<14>[   65.812278][ T1@C6] init: Service 'nfc_hal_service.tms.aidl' (pid 394) received signal 15
<14>[   65.812302][ T1@C6] init: Sending signal 9 to service 'nfc_hal_service.tms.aidl' (pid 394) process group...
<14>[   65.813037][ T1@C6] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1027/pid_394
<14>[   65.815148][ T1@C6] init: Service 'vendor.teensproxy' (pid 395) received signal 15
<14>[   65.815172][ T1@C6] init: Sending signal 9 to service 'vendor.teensproxy' (pid 395) process group...
<11>[   65.815300][ T1@C6] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_395/cgroup.kill: No such file or directory
<11>[   65.815369][ T1@C6] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_395/cgroup.procs: No such file or directory
<14>[   65.817146][ T1@C6] init: Service 'ylog' (pid 397) received signal 9
<14>[   65.817171][ T1@C6] init: Sending signal 9 to service 'ylog' (pid 397) process group...
<14>[   65.817931][ T1@C6] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_397
<14>[   65.820240][ T1@C7] init: Service 'poweronlog' (pid 398) received signal 15 oneshot service took 59.346001 seconds in background
<14>[   65.820269][ T1@C7] init: Sending signal 9 to service 'poweronlog' (pid 398) process group...
<14>[   65.821033][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_398
<14>[   65.822737][ T1@C7] init: Service 'srmi_proxyd' (pid 515) received signal 15
<14>[   65.822765][ T1@C7] init: Sending signal 9 to service 'srmi_proxyd' (pid 515) process group...
<14>[   65.823500][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_515
<14>[   65.825533][ T1@C7] init: Service 'watchdogd' (pid 523) exited with status 1 oneshot service took 0.175000 seconds in background
<14>[   65.825560][ T1@C7] init: Sending signal 9 to service 'watchdogd' (pid 523) process group...
<14>[   65.826443][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_523
<14>[   65.828295][ T1@C6] init: Untracked pid 409 received signal 15
<14>[   65.828322][ T1@C6] init: Untracked pid 409 did not have an associated service entry and will not be reaped
<14>[   65.828731][ T1@C6] init: Untracked pid 410 received signal 15
<14>[   65.828747][ T1@C6] init: Untracked pid 410 did not have an associated service entry and will not be reaped
<14>[   65.829147][ T1@C6] init: Untracked pid 412 received signal 15
<14>[   65.829163][ T1@C6] init: Untracked pid 412 did not have an associated service entry and will not be reaped
<14>[   65.829556][ T1@C6] init: Untracked pid 411 received signal 15
<14>[   65.829572][ T1@C6] init: Untracked pid 411 did not have an associated service entry and will not be reaped
<14>[   65.830003][ T1@C6] init: Untracked pid 426 exited with status 143
<14>[   65.830021][ T1@C6] init: Untracked pid 426 did not have an associated service entry and will not be reaped
<14>[   65.830319][ T1@C6] init: Untracked pid 431 exited with status 143
<14>[   65.830335][ T1@C6] init: Untracked pid 431 did not have an associated service entry and will not be reaped
<14>[   65.830525][ T1@C6] init: Untracked pid 434 received signal 15
<14>[   65.830540][ T1@C6] init: Untracked pid 434 did not have an associated service entry and will not be reaped
<14>[   65.830826][ T1@C6] init: Untracked pid 438 exited with status 143
<14>[   65.830842][ T1@C6] init: Untracked pid 438 did not have an associated service entry and will not be reaped
<14>[   65.831048][ T1@C6] init: Untracked pid 440 received signal 15
<14>[   65.831064][ T1@C6] init: Untracked pid 440 did not have an associated service entry and will not be reaped
<14>[   65.831349][ T1@C6] init: Untracked pid 432 received signal 15
<14>[   65.831364][ T1@C6] init: Untracked pid 432 did not have an associated service entry and will not be reaped
<14>[   65.831664][ T1@C6] init: Untracked pid 436 exited with status 143
<14>[   65.831679][ T1@C6] init: Untracked pid 436 did not have an associated service entry and will not be reaped
<14>[   65.831858][ T1@C6] init: Untracked pid 422 received signal 15
<14>[   65.831874][ T1@C6] init: Untracked pid 422 did not have an associated service entry and will not be reaped
<14>[   65.998911][ T1@C2] init: Service 'lmkd' (pid 290) received signal 15
<14>[   65.998969][ T1@C2] init: Sending signal 9 to service 'lmkd' (pid 290) process group...
<14>[   66.000531][ T1@C2] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1069/pid_290
<14>[   66.004164][ T1@C2] init: Waiting for 22 pids to be reaped took 253ms with 0 of them still running
<14>[   66.004339][ T1@C2] init: Stopping 220 services by sending SIGKILL
<14>[   66.004618][ T1@C2] init: Calling /system/bin/vdc volume abort_fuse
<6>[   66.006288][T10@C0] musb-sprd 64900000.usb: sm_work: runtime_suspending state
<6>[   66.006315][T10@C0] musb-sprd 64900000.usb: waiting glue suspended
<3>[   66.031086][T87@C2] charger-manager charger-manager: Can not to get board temperature, return init_temp=250
<4>[   66.031125][T87@C2] charger-manager charger-manager: failed to get board temperature
<6>[   66.031379][T87@C2] charger-manager charger-manager: vbat: 4371000, vbat_avg: 4399000, OCV: 4408000, ibat: -358000, ibat_avg: -84000, ibus: -22, vbus: 0, msoc: 1000, chg_sts: 2, frce_full: 0, chg_lmt_cur: 0, inpt_lmt_cur: 0, chgr_type: 0, Tboard: 250, Tbatt: 304, thm_cur: -22, thm_pwr: 0, is_fchg: 0, fchg_en: 0, tflush: 16, tperiod: 16
<6>[   66.032276][T87@C2] charger-manager charger-manager: new_uisoc = 1000, old_uisoc = 1000, work_cycle = 15s, cap_one_time = 30s
<15>[   66.078005][T532@C7] vdc: Waited 0ms for vold
<14>[   66.084251][ T1@C7] init: Calling /system/bin/vdc volume shutdown
<15>[   66.139786][T533@C7] vdc: Waited 0ms for vold
<14>[   66.146419][ T1@C7] init: Sending signal 9 to service 'vold' (pid 525) process group...
<14>[   66.163707][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_525
<14>[   66.165262][ T1@C7] init: Stopping 4 services by sending SIGKILL
<14>[   66.165354][ T1@C7] init: Sending signal 9 to service 'console' (pid 297) process group...
<14>[   66.169110][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_2000/pid_297
<14>[   66.170809][ T1@C7] init: Sending signal 9 to service 'logd' (pid 289) process group...
<14>[   66.187900][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1036/pid_289
<14>[   66.189688][ T1@C7] init: Subcontext received signal 15
<14>[   66.189715][ T1@C7] init: Subcontext did not have an associated service entry and will not be reaped
<14>[   66.190792][ T1@C7] init: Service 'logd' (pid 289) received signal 9
<14>[   66.190817][ T1@C7] init: Sending signal 9 to service 'logd' (pid 289) process group...
<11>[   66.190981][ T1@C7] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1036/pid_289/cgroup.kill: No such file or directory
<11>[   66.191055][ T1@C7] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1036/pid_289/cgroup.procs: No such file or directory
<14>[   66.194775][ T1@C7] init: Service 'console' (pid 297) received signal 9
<14>[   66.194804][ T1@C7] init: Sending signal 9 to service 'console' (pid 297) process group...
<11>[   66.194931][ T1@C7] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_2000/pid_297/cgroup.kill: No such file or directory
<11>[   66.195001][ T1@C7] libprocessgroup: Failed to open /sys/fs/cgroup/uid_2000/pid_297/cgroup.procs: No such file or directory
<14>[   66.196857][ T1@C7] init: Service 'vold' (pid 525) received signal 9
<14>[   66.196884][ T1@C7] init: Sending signal 9 to service 'vold' (pid 525) process group...
<11>[   66.196997][ T1@C7] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_525/cgroup.kill: No such file or directory
<11>[   66.197065][ T1@C7] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_525/cgroup.procs: No such file or directory
<11>[   66.197091][ T1@C7] init: Service vold has 'reboot_on_failure' option and failed, shutting down system.
<14>[   66.200304][ T1@C7] init: sync() before umount...
<6>[   66.210251][T10@C2] musb-sprd 64900000.usb: sm_work: runtime_suspending state
<6>[   66.210291][T10@C2] musb-sprd 64900000.usb: waiting glue suspended
<14>[   66.218361][ T1@C7] init: sync() before umount took18ms
<14>[   66.218464][ T1@C7] init: Ready to unmount apexes. So far shutdown sequence took 580ms
<6>[   66.224322][T87@C2] musb-hdrc musb-hdrc.1.auto: musb runtime suspend
<6>[   66.224650][T87@C2] musb-sprd 64900000.usb: enter into idle mode
<6>[   66.224713][T87@C2] musb-sprd 64900000.usb: musb_sprd_runtime_suspend: enter
<6>[   66.224761][T87@C2] musb-sprd 64900000.usb: musb_sprd_suspend: enter
<14>[   66.285743][T534@C7] apexd: Started. subcommand = --unmount-all
<14>[   66.285874][T534@C7] apexd-unmount-all: Populating APEX database from mounts...
<14>[   66.287141][T534@C7] apexd-unmount-all: Found "/apex/com.android.tzdata@351400020" backed by file /system/apex/com.google.android.tzdata6.apex
<14>[   66.287399][T534@C7] apexd-unmount-all: Found "/apex/com.android.vndk.v33@1" backed by file /system_ext/apex/com.android.vndk.v33.apex
<14>[   66.287637][T534@C7] apexd-unmount-all: Found "/apex/com.android.runtime@1" backed by file /system/apex/com.android.runtime.apex
<14>[   66.287863][T534@C7] apexd-unmount-all: Found "/apex/com.android.i18n@1" backed by file /system/apex/com.android.i18n.apex
<14>[   66.288149][T534@C7] apexd-unmount-all: 4 packages restored.
<14>[   66.288207][T534@C7] apexd-unmount-all: Unmounting /system/apex/com.android.i18n.apex mounted on /apex/com.android.i18n@1
<14>[   66.319453][T534@C6] apexd-unmount-all: Unmounting /system/apex/com.android.runtime.apex mounted on /apex/com.android.runtime@1
<11>[   66.320194][T534@C6] apexd-unmount-all: Failed to unmount bind-mount /apex/com.android.runtime: Device or resource busy
<14>[   66.320224][T534@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.tzdata6.apex mounted on /apex/com.android.tzdata@351400020
<14>[   66.347619][T534@C6] apexd-unmount-all: Unmounting /system_ext/apex/com.android.vndk.v33.apex mounted on /apex/com.android.vndk.v33@1
<11>[   66.348345][T534@C6] apexd-unmount-all: Failed to unmount bind-mount /apex/com.android.vndk.v33: Device or resource busy
<14>[   66.355552][ T1@C0] apexd: apexd terminated by exit(1)
<14>[   66.355552][ T1@C0] 
<11>[   66.356193][ T1@C0] init: '/system/bin/apexd --unmount-all' failed : 256
<14>[   66.386316][ T1@C6] init: Unmounting /dev/block/mmcblk0p48:/blackbox opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,alloc_mode=reuse,checkpoint_merge,fsync_mode=posix,discard_unit=block,memory=normal
<6>[   66.418349][T10@C4] musb-sprd 64900000.usb: sm_work: runtime_suspending state
<6>[   66.418479][T10@C4] musb-sprd 64900000.usb: sm_work: idle state
<14>[   66.541715][ T1@C7] init: Umounted /dev/block/mmcblk0p48:/blackbox opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,alloc_mode=reuse,checkpoint_merge,fsync_mode=posix,discard_unit=block,memory=normal
<14>[   66.541752][ T1@C7] init: Unmounting /dev/block/mmcblk0p47:/cache opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,alloc_mode=reuse,checkpoint_merge,fsync_mode=posix,discard_unit=block,memory=normal
<14>[   66.592132][ T1@C6] init: Umounted /dev/block/mmcblk0p47:/cache opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,alloc_mode=reuse,checkpoint_merge,fsync_mode=posix,discard_unit=block,memory=normal
<14>[   66.592190][ T1@C6] init: Unmounting /dev/block/mmcblk0p1:/mnt/vendor opts rw,seclabel,nosuid,nodev,noatime,noauto_da_alloc
<14>[   66.597641][ T1@C6] init: Umounted /dev/block/mmcblk0p1:/mnt/vendor opts rw,seclabel,nosuid,nodev,noatime,noauto_da_alloc
<14>[   66.597679][ T1@C6] init: Unmounting /dev/block/mmcblk0p51:/metadata opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,alloc_mode=reuse,checkpoint_merge,fsync_mode=posix,discard_unit=block,memory=normal
<14>[   66.627378][ T1@C7] init: Umounted /dev/block/mmcblk0p51:/metadata opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,alloc_mode=reuse,checkpoint_merge,fsync_mode=posix,discard_unit=block,memory=normal
<14>[   66.627417][ T1@C7] init: sync() after umount...
<14>[   66.628061][ T1@C7] init: sync() after umount took0ms
<12>[   66.728560][ T1@C7] init: powerctl_shutdown_time_ms:1090:0
<14>[   66.729027][T520@C5] init: remaining_shutdown_time: 305
<3>[   66.729380][ T1@C7] shutdown_detect_check: shutdown_detect_phase: shutdown  current phase systemcall
<14>[   66.729435][ T1@C7] init: Reboot ending, jumping to kernel
<3>[   66.729604][ T1@C7] failed to open '/dev/block/by-name/sd_klog':-2!
BpBinder: onLastStrongRef automatically unlinking death recipients: 

YZIPC02lastlog/lastylogbuffer.log20CPIZY

 is ylog buffer. Now , it is nothing . This is ylog buffer. Now , it is nothing . 