
07-26 11:12:02.740 I/        (    0): [T14370@C0] [drm] esd_promixity_satus:0, fts_proximity:0
07-26 11:12:03.903 I/DialerInCallProximityCallScopedController( 9545): com.android.dialer.incall.proximitysensor.impl.incall.InCallProximityCallScopedController.onCallScopeAdded:37 registering DialerCall_5 to DisplayStateTracker and AccelerometerTracker.
07-26 11:12:03.904 I/DialerDisplayStateTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.DisplayStateTracker.registerCall:116 registering displayListener
07-26 11:12:03.905 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:123 enable
07-26 11:12:03.905 D/SensorManager( 9545): registerListenerImpl sensor = {Sensor name="acc_sc7a20", vendor="Silan", version=1, type=1, maxRange=39.2266, resolution=0.0096, power=0.17, minDelay=5000}, delayUs = 200000, latencyUs = 0, listener = kxw@bdd775c
07-26 11:12:03.865 I/        (    0): [T629@C3] sprd-sensor sensorhub: buf=1 0 200000 0
07-26 11:12:03.865 I/        (    0): [T629@C3] sprd-sensor sensorhub: handle = 1, rate = 200000, batch_latency = 0
07-26 11:12:03.914 D/SensorHub(  629): SPRD[786]batch::handle[1], flags[0], period_ns[200000000], timeout[0]
07-26 11:12:03.939 I/DialerInCallProximityController( 9545): com.android.dialer.incall.proximitysensor.impl.incall.InCallProximityController.turnOnProximitySensor:117 acquiring wake lock
07-26 11:12:03.948 E/SensorManager( 9545): com.google.android.dialer can't registerListenerImpl proximity !!!!!
07-26 11:12:03.948 D/SensorManager( 1240): registerListenerImpl sensor = {Sensor name="proximity", vendor="Sprd Group Ltd.", version=1, type=8, maxRange=5.0, resolution=1.0, power=0.15, minDelay=0}, delayUs = 0, latencyUs = 0, listener = com.android.server.display.DisplayPowerProximityStateController$1@7138706
07-26 11:12:03.948 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:123 enable
07-26 11:12:03.949 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:125 sensorEventListener already enabled.
07-26 11:12:03.961 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:123 enable
07-26 11:12:03.961 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:125 sensorEventListener already enabled.
07-26 11:12:03.966 D/SensorHub(  629): SPRD[786]batch::handle[8], flags[0], period_ns[1000000], timeout[0]
07-26 11:12:03.966 D/SensorHub(  629): SPRD[549]sendCmdtoDriverIoctl::Cmdbatch
07-26 11:12:03.961 W/[T629@C3] [FTS_TS]fts_ps_ioctl(    0): proximity: cmd 2
07-26 11:12:04.011 D/SensorHub(  629): SPRD[731]setEnable::handle[8], enabled[1]
07-26 11:12:04.196 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:123 enable
07-26 11:12:04.196 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:125 sensorEventListener already enabled.
07-26 11:12:04.259 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:123 enable
07-26 11:12:04.260 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:125 sensorEventListener already enabled.
07-26 11:12:04.422 I/DialerDisplayStateTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.DisplayStateTracker$1.onDisplayChanged:78 set isDisplayOn to true
07-26 11:12:04.449 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:123 enable
07-26 11:12:04.449 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:125 sensorEventListener already enabled.
07-26 11:12:04.458 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:123 enable
07-26 11:12:04.458 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:125 sensorEventListener already enabled.
07-26 11:12:04.629 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:123 enable
07-26 11:12:04.629 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:125 sensorEventListener already enabled.
07-26 11:12:04.647 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:123 enable
07-26 11:12:04.648 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:125 sensorEventListener already enabled.
07-26 11:12:04.741 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:123 enable
07-26 11:12:04.741 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:125 sensorEventListener already enabled.
07-26 11:12:04.883 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:123 enable
07-26 11:12:04.883 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:125 sensorEventListener already enabled.
07-26 11:12:04.889 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.updateOrientation:276 orientation is updated to HORIZONTAL
07-26 11:12:04.903 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:123 enable
07-26 11:12:04.904 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:125 sensorEventListener already enabled.
07-26 11:12:04.944 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:123 enable
07-26 11:12:04.944 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:125 sensorEventListener already enabled.
07-26 11:12:04.961 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:123 enable
07-26 11:12:04.961 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:125 sensorEventListener already enabled.
07-26 11:12:04.970 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:123 enable
07-26 11:12:04.971 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:125 sensorEventListener already enabled.
07-26 11:12:05.364 I/        (    0): [T14937@C2] [drm] esd_promixity_satus:0, fts_proximity:1
07-26 11:12:07.382 I/        (    0): [T14937@C2] [drm] esd_promixity_satus:0, fts_proximity:1
07-26 11:12:07.479 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:123 enable
07-26 11:12:07.479 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:125 sensorEventListener already enabled.
07-26 11:12:07.486 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:123 enable
07-26 11:12:07.486 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:125 sensorEventListener already enabled.
07-26 11:12:07.505 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:123 enable
07-26 11:12:07.505 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:125 sensorEventListener already enabled.
07-26 11:12:07.512 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:123 enable
07-26 11:12:07.512 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:125 sensorEventListener already enabled.
07-26 11:12:07.530 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:123 enable
07-26 11:12:07.530 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:125 sensorEventListener already enabled.
07-26 11:12:09.400 I/        (    0): [T14937@C2] [drm] esd_promixity_satus:0, fts_proximity:1
07-26 11:12:11.418 I/        (    0): [T14937@C2] [drm] esd_promixity_satus:0, fts_proximity:1
07-26 11:12:12.127 D/SensorHub(  629): SPRD[894]processEventInput::PS  sensor: 0.000000
07-26 11:12:12.135 D/DisplayPowerProximityStateController[0]( 1240): Proximity Sensor changed distance= 0.0 positive= true
07-26 11:12:12.136 I/DisplayPowerProximityStateController[0]( 1240): No longer ignoring proximity [1]
07-26 11:12:12.136 D/DisplayPowerState( 1240): setColorFadeLevel: level=0.0,mScreenState is 2,mColorFadePrepared is false
07-26 11:12:12.141 D/SensorManager( 1240): unregisterListenerImpl sensor = null, listener = com.android.server.display.brightness.clamper.BrightnessClamperController$1@59b3b5b
07-26 11:12:12.146 V/DisplayPowerController2[0]( 1240): Brightness [0.0] reason changing to: 'screen_off', previous reason: 'manual'.
07-26 11:12:12.147 I/DisplayPowerController2[0]( 1240): BrightnessEvent: disp=0, physDisp=local:4619827259835644672, displayState=OFF, displayPolicy=BRIGHT, brt=0.0, initBrt=0.4015748, rcmdBrt=NaN, preBrt=NaN, lux=0.0, preLux=0.0, hbmMax=1.0, hbmMode=off, rbcStrength=50, thrmMax=1.0, powerFactor=0.7, wasShortTermModelActive=false, flags=, reason=screen_off, autoBrightness=false, strategy=ScreenOffBrightnessStrategy, autoBrightnessMode=default
07-26 11:12:12.103 I/[T645@C5] [FTS_TS/I]fts_ts_suspend(    0): Suspend device proximity_enable(fts_dev):1,fts_nomal_suspend_flag:0,fts_complete_suspend:0
07-26 11:12:12.104 I/[T645@C5] [FTS_TS/I]fts_ts_suspend(    0): proximity mode suspend return!
07-26 11:12:12.157 D/SensorManager( 1240): unregisterListenerImpl sensor = null, listener = com.android.server.display.mode.DisplayModeDirector$BrightnessObserver$LightSensorEventListener@7c6b8aa
07-26 11:12:12.164 I/DialerDisplayStateTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.DisplayStateTracker$1.onDisplayChanged:78 set isDisplayOn to false
07-26 11:12:12.119 I/        (    0): [T706@C1] [drm] sprd_panel_disable() :sleep in proximity mode
07-26 11:12:12.181 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.disable:158 disable
07-26 11:12:12.181 D/SensorManager( 9545): unregisterListenerImpl sensor = null, listener = kxw@bdd775c
07-26 11:12:12.183 D/SensorHub(  629): SPRD[786]batch::handle[1], flags[0], period_ns[200000000], timeout[2000000000]
07-26 11:12:12.133 I/        (    0): [T629@C3] sprd-sensor sensorhub: buf=1 0 200000 2000000
07-26 11:12:12.133 I/        (    0): [T629@C3] sprd-sensor sensorhub: handle = 1, rate = 200000, batch_latency = 2000000
07-26 11:12:12.186 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.disable:167 resetting orientation
07-26 11:12:12.137 I/        (    0): [T706@C0] [drm] sprd_panel_unprepare() :proximity active && no need to set voltage!!
07-26 11:12:12.206 D/SensorManager( 1240): unregisterListenerImpl sensor = null, listener = com.android.server.display.brightness.clamper.BrightnessClamperController$1@59b3b5b








07-26 11:12:20.232 D/SensorHub(  629): SPRD[894]processEventInput::PS  sensor: 5.000000
07-26 11:12:20.240 D/DisplayPowerProximityStateController[0]( 1240): Proximity Sensor changed distance= 5.0 positive= false
07-26 11:12:20.341 I/DisplayPowerProximityStateController[0]( 1240): No longer ignoring proximity [0]
07-26 11:12:20.345 D/DisplayPowerState( 1240): setColorFadeLevel: level=1.0,mScreenState is 2,mColorFadePrepared is false
07-26 11:12:20.350 V/DisplayPowerController2[0]( 1240): Brightness [0.4015748] reason changing to: 'manual', previous reason: 'screen_off'.
07-26 11:12:20.352 I/DisplayPowerController2[0]( 1240): BrightnessEvent: disp=0, physDisp=local:4619827259835644672, displayState=ON, displayPolicy=BRIGHT, brt=0.4015748, initBrt=0.0, rcmdBrt=NaN, preBrt=NaN, lux=0.0, preLux=0.0, hbmMax=1.0, hbmMode=off, rbcStrength=50, thrmMax=1.0, powerFactor=0.7, wasShortTermModelActive=false, flags=, reason=manual, autoBrightness=false, strategy=InvalidBrightnessStrategy, autoBrightnessMode=default
07-26 11:12:20.372 I/DialerDisplayStateTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.DisplayStateTracker$1.onDisplayChanged:78 set isDisplayOn to true
07-26 11:12:20.383 D/SensorManager( 1240): unregisterListenerImpl sensor = null, listener = com.android.server.display.mode.DisplayModeDirector$BrightnessObserver$LightSensorEventListener@7c6b8aa
07-26 11:12:20.384 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:123 enable
07-26 11:12:20.384 D/SensorManager( 9545): registerListenerImpl sensor = {Sensor name="acc_sc7a20", vendor="Silan", version=1, type=1, maxRange=39.2266, resolution=0.0096, power=0.17, minDelay=5000}, delayUs = 200000, latencyUs = 0, listener = kxw@bdd775c
07-26 11:12:20.392 D/SensorHub(  629): SPRD[786]batch::handle[1], flags[0], period_ns[200000000], timeout[0]
07-26 11:12:20.342 I/        (    0): [T629@C3] sprd-sensor sensorhub: buf=1 0 200000 0
07-26 11:12:20.342 I/        (    0): [T629@C3] sprd-sensor sensorhub: handle = 1, rate = 200000, batch_latency = 0
07-26 11:12:20.357 I/        (    0): [T706@C2] [drm] sprd_panel_prepare() :fts_proximity = 1 | proximity_write_fail = 0
07-26 11:12:20.358 I/        (    0): [T706@C2] [drm] sprd_panel_prepare() :proximity active && no need to set voltage!!
07-26 11:12:20.358 I/        (    0): [T706@C2] [drm] sprd_panel_enable() fts_proximity=1 skip
07-26 11:12:20.519 W/[T426@C2] [FTS_TS]fts_ts_resume(    0): proximity resume fts_nomal_suspend_flag:0,proximity_enable(fts_dev):1,fts_complete_suspend = 0
07-26 11:12:21.067 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.updateOrientation:276 orientation is updated to HORIZONTAL
07-26 11:12:21.076 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:123 enable
07-26 11:12:21.077 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:125 sensorEventListener already enabled.
07-26 11:12:21.547 I/        (    0): [T14937@C2] [drm] esd_promixity_satus:0, fts_proximity:1










07-26 11:12:23.565 I/        (    0): [T14937@C2] [drm] esd_promixity_satus:0, fts_proximity:1
07-26 11:12:25.583 I/        (    0): [T14937@C2] [drm] esd_promixity_satus:0, fts_proximity:1
07-26 11:12:27.075 D/SensorHub(  629): SPRD[894]processEventInput::PS  sensor: 0.000000
07-26 11:12:27.083 D/DisplayPowerProximityStateController[0]( 1240): Proximity Sensor changed distance= 0.0 positive= true
07-26 11:12:27.084 I/DisplayPowerProximityStateController[0]( 1240): No longer ignoring proximity [1]
07-26 11:12:27.085 D/DisplayPowerState( 1240): setColorFadeLevel: level=0.0,mScreenState is 2,mColorFadePrepared is false
07-26 11:12:27.092 D/SensorManager( 1240): unregisterListenerImpl sensor = null, listener = com.android.server.display.brightness.clamper.BrightnessClamperController$1@59b3b5b
07-26 11:12:27.097 V/DisplayPowerController2[0]( 1240): Brightness [0.0] reason changing to: 'screen_off', previous reason: 'manual'.
07-26 11:12:27.099 I/DisplayPowerController2[0]( 1240): BrightnessEvent: disp=0, physDisp=local:4619827259835644672, displayState=OFF, displayPolicy=BRIGHT, brt=0.0, initBrt=0.4015748, rcmdBrt=NaN, preBrt=NaN, lux=0.0, preLux=0.0, hbmMax=1.0, hbmMode=off, rbcStrength=50, thrmMax=1.0, powerFactor=0.7, wasShortTermModelActive=false, flags=, reason=screen_off, autoBrightness=false, strategy=ScreenOffBrightnessStrategy, autoBrightnessMode=default
07-26 11:12:27.055 I/[T645@C3] [FTS_TS/I]fts_ts_suspend(    0): Suspend device proximity_enable(fts_dev):1,fts_nomal_suspend_flag:0,fts_complete_suspend:0
07-26 11:12:27.055 I/[T645@C3] [FTS_TS/I]fts_ts_suspend(    0): proximity mode suspend return!
07-26 11:12:27.109 D/SensorManager( 1240): unregisterListenerImpl sensor = null, listener = com.android.server.display.mode.DisplayModeDirector$BrightnessObserver$LightSensorEventListener@7c6b8aa
07-26 11:12:27.116 I/DialerDisplayStateTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.DisplayStateTracker$1.onDisplayChanged:78 set isDisplayOn to false
07-26 11:12:27.067 I/        (    0): [T706@C0] [drm] sprd_panel_disable() :sleep in proximity mode
07-26 11:12:27.134 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.disable:158 disable
07-26 11:12:27.135 D/SensorManager( 9545): unregisterListenerImpl sensor = null, listener = kxw@bdd775c
07-26 11:12:27.138 D/SensorHub(  629): SPRD[786]batch::handle[1], flags[0], period_ns[200000000], timeout[2000000000]
07-26 11:12:27.085 I/        (    0): [T706@C0] [drm] sprd_panel_unprepare() :proximity active && no need to set voltage!!
07-26 11:12:27.088 I/        (    0): [T629@C3] sprd-sensor sensorhub: buf=1 0 200000 2000000
07-26 11:12:27.088 I/        (    0): [T629@C3] sprd-sensor sensorhub: handle = 1, rate = 200000, batch_latency = 2000000
07-26 11:12:27.140 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.disable:167 resetting orientation
07-26 11:12:27.152 D/SensorManager( 1240): unregisterListenerImpl sensor = null, listener = com.android.server.display.brightness.clamper.BrightnessClamperController$1@59b3b5b
07-26 11:12:27.193 D/SensorHub(  629): SPRD[894]processEventInput::PS  sensor: 5.000000
07-26 11:12:27.197 D/DisplayPowerProximityStateController[0]( 1240): Proximity Sensor changed distance= 5.0 positive= false
07-26 11:12:27.297 I/DisplayPowerProximityStateController[0]( 1240): No longer ignoring proximity [0]
07-26 11:12:27.301 D/DisplayPowerState( 1240): setColorFadeLevel: level=1.0,mScreenState is 2,mColorFadePrepared is false
07-26 11:12:27.305 V/DisplayPowerController2[0]( 1240): Brightness [0.4015748] reason changing to: 'manual', previous reason: 'screen_off'.
07-26 11:12:27.306 I/DisplayPowerController2[0]( 1240): BrightnessEvent: disp=0, physDisp=local:4619827259835644672, displayState=ON, displayPolicy=BRIGHT, brt=0.4015748, initBrt=0.0, rcmdBrt=NaN, preBrt=NaN, lux=0.0, preLux=0.0, hbmMax=1.0, hbmMode=off, rbcStrength=50, thrmMax=1.0, powerFactor=0.7, wasShortTermModelActive=false, flags=, reason=manual, autoBrightness=false, strategy=InvalidBrightnessStrategy, autoBrightnessMode=default
07-26 11:12:27.325 I/DialerDisplayStateTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.DisplayStateTracker$1.onDisplayChanged:78 set isDisplayOn to true
07-26 11:12:27.332 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:123 enable
07-26 11:12:27.333 D/SensorManager( 9545): registerListenerImpl sensor = {Sensor name="acc_sc7a20", vendor="Silan", version=1, type=1, maxRange=39.2266, resolution=0.0096, power=0.17, minDelay=5000}, delayUs = 200000, latencyUs = 0, listener = kxw@bdd775c
07-26 11:12:27.337 D/SensorManager( 1240): unregisterListenerImpl sensor = null, listener = com.android.server.display.mode.DisplayModeDirector$BrightnessObserver$LightSensorEventListener@7c6b8aa
07-26 11:12:27.293 I/        (    0): [T629@C1] sprd-sensor sensorhub: buf=1 0 200000 0
07-26 11:12:27.293 I/        (    0): [T629@C1] sprd-sensor sensorhub: handle = 1, rate = 200000, batch_latency = 0
07-26 11:12:27.342 D/SensorHub(  629): SPRD[786]batch::handle[1], flags[0], period_ns[200000000], timeout[0]
07-26 11:12:27.311 I/        (    0): [T706@C3] [drm] sprd_panel_prepare() :fts_proximity = 1 | proximity_write_fail = 0
07-26 11:12:27.311 I/        (    0): [T706@C3] [drm] sprd_panel_prepare() :proximity active && no need to set voltage!!
07-26 11:12:27.311 I/        (    0): [T706@C3] [drm] sprd_panel_enable() fts_proximity=1 skip
07-26 11:12:27.471 W/[T426@C5] [FTS_TS]fts_ts_resume(    0): proximity resume fts_nomal_suspend_flag:0,proximity_enable(fts_dev):1,fts_complete_suspend = 0
07-26 11:12:27.953 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.updateOrientation:276 orientation is updated to HORIZONTAL
07-26 11:12:27.961 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:123 enable
07-26 11:12:27.961 I/DialerAccelerometerTracker( 9545): com.android.dialer.incall.proximitysensor.impl.incall.AccelerometerTracker.enable:125 sensorEventListener already enabled.
07-26 11:12:28.499 I/        (    0): [T15054@C3] [drm] esd_promixity_satus:0, fts_proximity:1


07-26 11:12:30.517 I/        (    0): [T15054@C3] [drm] esd_promixity_satus:0, fts_proximity:1

