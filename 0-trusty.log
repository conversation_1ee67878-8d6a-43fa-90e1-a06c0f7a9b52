5> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  403.026289> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  403.026312> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  403.026319> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  499.594678> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  499.594693> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  499.594924> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  499.594934> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  499.595400> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  499.595410> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  499.595427> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  499.595434> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  499.721641> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  499.721656> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  499.721890> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  499.721899> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  499.722365> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  499.722375> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  499.722391> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  499.722398> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  500.126528> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  500.126542> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  500.126772> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  500.126781> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  500.127226> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  500.127237> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  500.127253> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  500.127261> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  500.281788> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  500.281802> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  500.282348> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  500.282361> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  500.282834> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  500.282845> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  500.282864> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  500.282871> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  500.373485> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  500.373498> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  500.373729> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  500.373739> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  500.374192> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  500.374201> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  500.374215> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  500.374223> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
< 3474.875735> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 3474.875759> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
< 3474.876554> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 3474.876578> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
< 3474.877509> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 3474.877530> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
< 3474.877569> ss-ipc: 185: do_disconnect ev->handle ox3f3
< 3474.877583> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
< 3474.997019> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 3474.997041> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
< 3474.997455> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 3474.997474> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
< 3474.998200> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 3474.998218> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
< 3474.998248> ss-ipc: 185: do_disconnect ev->handle ox3f3
< 3474.998260> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
< 3475.070960> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 3475.070976> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
< 3475.071228> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 3475.071239> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
< 3475.071716> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 3475.071728> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
< 3475.071749> ss-ipc: 185: do_disconnect ev->handle ox3f3
< 3475.071757> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
< 3475.128356> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 3475.128489> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
< 3475.128808> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 3475.128818> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
< 3475.129285> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 3475.129296> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
< 3475.129312> ss-ipc: 185: do_disconnect ev->handle ox3f3
< 3475.129321> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<15036.842243> ss-ipc: 185: do_disconnect ev->handle ox3f5
<15036.842261> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<15036.842522> ss-ipc: 185: do_disconnect ev->handle ox3f5
<15036.842535> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<15036.843027> ss-ipc: 185: do_disconnect ev->handle ox3f5
<15036.843040> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<15036.843062> ss-ipc: 185: do_disconnect ev->handle ox3f3
<15036.843070> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<15037.416816> ss-ipc: 185: do_disconnect ev->handle ox3f5
<15037.416829> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<15037.416971> ss-ipc: 185: do_disconnect ev->handle ox3f5
<15037.416978> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<15037.417394> ss-ipc: 185: do_disconnect ev->handle ox3f5
<15037.417401> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<15037.417413> ss-ipc: 185: do_disconnect ev->handle ox3f3
<15037.417419> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<15038.191609> ss-ipc: 185: do_disconnect ev->handle ox3f5
<15038.191631> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<15038.191912> ss-ipc: 185: do_disconnect ev->handle ox3f5
<15038.191926> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<15038.192732> ss-ipc: 185: do_disconnect ev->handle ox3f5
<15038.192757> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<15038.192818> ss-ipc: 185: do_disconnect ev->handle ox3f3
<15038.192826> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<15038.255435> ss-ipc: 185: do_disconnect ev->handle ox3f5
<15038.255461> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<15038.255737> ss-ipc: 185: do_disconnect ev->handle ox3f5
<15038.255747> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<15038.256773> ss-ipc: 185: do_disconnect ev->handle ox3f5
<15038.256790> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<15038.256823> ss-ipc: 185: do_disconnect ev->handle ox3f3
<15038.256831> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<15038.480739> ss-ipc: 185: do_disconnect ev->handle ox3f5
<15038.480758> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<15038.480997> ss-ipc: 185: do_disconnect ev->handle ox3f5
<15038.481006> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<15038.481518> ss-ipc: 185: do_disconnect ev->handle ox3f5
<15038.481529> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<15038.481544> ss-ipc: 185: do_disconnect ev->handle ox3f3
<15038.481553> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<15040.560382> ss-ipc: 185: do_disconnect ev->handle ox3f5
<15040.560403> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<15040.560696> ss-ipc: 185: do_disconnect ev->handle ox3f5
<15040.560707> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<15040.561226> ss-ipc: 185: do_disconnect ev->handle ox3f5
<15040.561236> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<15040.561252> ss-ipc: 185: do_disconnect ev->handle ox3f3
<15040.561260> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<15044.639559> ss-ipc: 185: do_disconnect ev->handle ox3f5
<15044.639580> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<15044.639842> ss-ipc: 185: do_disconnect ev->handle ox3f5
<15044.639854> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<15044.640805> ss-ipc: 185: do_disconnect ev->handle ox3f5
<15044.640828> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<15044.640876> ss-ipc: 185: do_disconnect ev->handle ox3f3
<15044.640885> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<46679.716164> ss-ipc: 185: do_disconnect ev->handle ox3f5
<46679.716182> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<46679.716441> ss-ipc: 185: do_disconnect ev->handle ox3f5
<46679.716452> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<46679.716936> ss-ipc: 185: do_disconnect ev->handle ox3f5
<46679.716948> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<46679.716966> ss-ipc: 185: do_disconnect ev->handle ox3f3
<46679.716975> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<46679.785542> ss-ipc: 185: do_disconnect ev->handle ox3f5
<46679.785560> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<46679.785796> ss-ipc: 185: do_disconnect ev->handle ox3f5
<46679.785806> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<46679.786272> ss-ipc: 185: do_disconnect ev->handle ox3f5
<46679.786283> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<46679.786302> ss-ipc: 185: do_disconnect ev->handle ox3f3
<46679.786310> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<51563.490082> ss-ipc: 185: do_disconnect ev->handle ox3f5
<51563.490105> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<51563.490357> ss-ipc: 185: do_disconnect ev->handle ox3f5
<51563.490367> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<51563.490853> ss-ipc: 185: do_disconnect ev->handle ox3f5
<51563.490863> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<51563.490879> ss-ipc: 185: do_disconnect ev->handle ox3f3
<51563.490887> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<51563.557226> ss-ipc: 185: do_disconnect ev->handle ox3f5
<51563.557248> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<51563.557527> ss-ipc: 185: do_disconnect ev->handle ox3f5
<51563.557537> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<51563.558297> ss-ipc: 185: do_disconnect ev->handle ox3f5
<51563.558313> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<51563.558352> ss-ipc: 185: do_disconnect ev->handle ox3f3
<51563.558361> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<69194.154238> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69194.154252> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<69194.154477> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69194.154487> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<69194.154912> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69194.154922> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<69194.154937> ss-ipc: 185: do_disconnect ev->handle ox3f3
<69194.154945> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<69194.230399> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69194.230413> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<69194.230641> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69194.230652> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<69194.231108> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69194.231120> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<69194.231139> ss-ipc: 185: do_disconnect ev->handle ox3f3
<69194.231148> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<69208.285546> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69208.285557> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<69208.285697> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69208.285705> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<69208.286013> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69208.286021> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<69208.286032> ss-ipc: 185: do_disconnect ev->handle ox3f3
<69208.286038> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<69208.440187> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69208.440204> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<69208.440475> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69208.440487> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<69208.440944> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69208.440956> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<69208.440974> ss-ipc: 185: do_disconnect ev->handle ox3f3
<69208.440982> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
[69225.361555] c5 tam_load_request:1513: load look up com.android.trusty.widevine
[69225.361568] c5 handle_conn_req:412: failed (-2) to send response
[69225.478922] c7 ta_manager_write_ta:857: ta_manager_write_ta: new ta!
[69225.478933] c7 ta_manager_aes_decrypt:362: ta_encrypted_compat:ta just signed!!!
[69225.494669] c6 ta_manager_write_ta:948: ta_manager_write_ta, verify ta
[69225.494678] c6 ta_manager_aes_decrypt:362: ta_encrypted_compat:ta just signed!!!
[69225.494682] c6 ta_manager_verify_img:447: RSA_hash
[69225.508487] c0 ta_manager_verify_img:506: RSA_verify
[69225.510634] c0 ta_manager_write_ta:979: ta_manager_write_ta, load ta
[69225.510646] c0 trusty_app:(32) start 0xffffffffe094c000 size 0x001d4000
[69225.510864] c0 trusty_app: whitelist.table 0x0, size: 0
[69225.510867] c0 trusty_app 17 uuid: 0x81af0b44 0x41f0 0x11e7 0xa919 0x92ebcb67fe33
[69225.510876] c0 trusty_app 0xffffffffe076cea8: stack_sz=0x10000
[69225.510879] c0 trusty_app 0xffffffffe076cea8: heap_sz=0x800000
[69225.510883] c0 trusty_app 0xffffffffe076cea8: one_shot=0x0
[69225.510886] c0 trusty_app 0xffffffffe076cea8: keep_alive=0x1
[69225.510889] c0 trusty_app 0xffffffffe076cea8: flags=0x1c
[69225.510892] c0 ta_manager_write_ta:985: enter tam anti rollback
[69225.510900] c0 tam_anti_rollback:259: ta anti_rollback not enable!!!
[69225.510904] c0 ta_manager_write_ta:997: tam anti rollback ok
[69225.510907] c0 ta_manager_write_ta:1009: current ta not define whilelist. not do loading authority check.
[69225.510912] c0 trusty_tapp_init:
[69225.512685] c0 trusty_app 17: code: start 0x00008000 end 0x000a7ed4
[69225.512697] c0 trusty_app 17: data: start 0x000a8000 end 0x001d9000
[69225.512701] c0 trusty_app 17: bss:                end 0x001d8624
[69225.512705] c0 trusty_app 17: brk:  start 0x001d9000 end 0x009d9000
[69225.512709] c0 trusty_app 17: entry 0x0002f09c
[69225.512759] c0 tam_port_publish:1501:  other port com.android.trusty.sec.widevine
[69225.512776] c0 tam_port_publish:1496: publish port com.android.trusty.widevine
[69225.512787] c0 ta_manager_write_ta:1052: ta_manager_write_ta, load com.android.trusty.widevine accomplished!
<69225.536724> trusty_unisoc_oemcrypto [E]: [WTPI_LoadRootOfTrust]161: widevine.ReadWidevineDeviceID failed len=0
<69225.536919> ss-ipc: 185: do_disconnect ev->handle ox3f6
<69225.536930> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<69225.555915> ss-ipc: 185: do_disconnect ev->handle ox3f3
<69225.555937> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<69225.555954> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69225.555963> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
[69225.563163] c7 tam_disc_notify:1554: port =  com.android.trusty.widevine
[69225.563172] c7 tam_disc_notify:1572: Keep alive TA will not disconnect.
<69225.654674> trusty_unisoc_oemcrypto [E]: [WTPI_LoadRootOfTrust]161: widevine.ReadWidevineDeviceID failed len=0
<69225.654929> ss-ipc: 185: do_disconnect ev->handle ox3f6
<69225.654945> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<69225.682867> ss-ipc: 185: do_disconnect ev->handle ox3f3
<69225.682884> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<69225.682896> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69225.682903> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<69298.163418> trusty_unisoc_oemcrypto [E]: [WTPI_LoadRootOfTrust]161: widevine.ReadWidevineDeviceID failed len=0
<69298.163710> ss-ipc: 185: do_disconnect ev->handle ox3f6
<69298.163728> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<69298.186694> ss-ipc: 185: do_disconnect ev->handle ox3f3
<69298.186737> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<69298.186775> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69298.186797> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<69298.585787> trusty_unisoc_oemcrypto [E]: [WTPI_LoadRootOfTrust]161: widevine.ReadWidevineDeviceID failed len=0
<69298.586075> ss-ipc: 185: do_disconnect ev->handle ox3f6
<69298.586095> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<69298.610512> ss-ipc: 185: do_disconnect ev->handle ox3f3
<69298.610536> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<69298.610555> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69298.610564> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<69299.368901> trusty_unisoc_oemcrypto [E]: [WTPI_LoadRootOfTrust]161: widevine.ReadWidevineDeviceID failed len=0
<69299.369351> ss-ipc: 185: do_disconnect ev->handle ox3f6
<69299.369380> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<69299.389906> ss-ipc: 185: do_disconnect ev->handle ox3f3
<69299.389938> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<69299.389963> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69299.389977> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<69299.463828> trusty_unisoc_oemcrypto [E]: [WTPI_LoadRootOfTrust]161: widevine.ReadWidevineDeviceID failed len=0
<69299.464484> ss-ipc: 185: do_disconnect ev->handle ox3f6
<69299.464510> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<69299.485827> ss-ipc: 185: do_disconnect ev->handle ox3f3
<69299.485872> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<69299.485909> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69299.485928> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<69349.229856> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69349.229876> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<69349.230179> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69349.230192> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<69349.230779> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69349.230793> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<69349.230814> ss-ipc: 185: do_disconnect ev->handle ox3f3
<69349.230825> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<69354.198760> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69354.198777> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<69354.199011> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69354.199022> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<69354.199465> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69354.199476> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<69354.199490> ss-ipc: 185: do_disconnect ev->handle ox3f3
<69354.199499> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<69594.540799> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69594.540833> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<69594.541412> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69594.541440> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<69594.542507> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69594.542533> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<69594.542571> ss-ipc: 185: do_disconnect ev->handle ox3f3
<69594.542594> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<69599.540247> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69599.540282> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<69599.540866> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69599.540887> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<69599.541761> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69599.541782> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<69599.541813> ss-ipc: 185: do_disconnect ev->handle ox3f3
<69599.541831> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<69893.327668> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69893.327703> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<69893.328599> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69893.328629> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<69893.329548> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69893.329572> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<69893.329609> ss-ipc: 185: do_disconnect ev->handle ox3f3
<69893.329627> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<69896.703854> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69896.703889> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<69896.704688> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69896.704717> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<69896.705797> ss-ipc: 185: do_disconnect ev->handle ox3f5
<69896.705823> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<69896.705863> ss-ipc: 185: do_disconnect ev->handle ox3f3
<69896.705888> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<70194.874946> ss-ipc: 185: do_disconnect ev->handle ox3f5
<70194.874964> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<70194.875248> ss-ipc: 185: do_disconnect ev->handle ox3f5
<70194.875261> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<70194.875778> ss-ipc: 185: do_disconnect ev->handle ox3f5
<70194.875791> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<70194.875811> ss-ipc: 185: do_disconnect ev->handle ox3f3
<70194.875822> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<70199.840421> ss-ipc: 185: do_disconnect ev->handle ox3f5
<70199.840449> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<70199.840815> ss-ipc: 185: do_disconnect ev->handle ox3f5
<70199.840825> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<70199.841306> ss-ipc: 185: do_disconnect ev->handle ox3f5
<70199.841316> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<70199.841333> ss-ipc: 185: do_disconnect ev->handle ox3f3
<70199.841341> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<70506.420658> ss-ipc: 185: do_disconnect ev->handle ox3f5
<70506.420692> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<70506.421268> ss-ipc: 185: do_disconnect ev->handle ox3f5
<70506.421296> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<70506.422311> ss-ipc: 185: do_disconnect ev->handle ox3f5
<70506.422339> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<70506.422379> ss-ipc: 185: do_disconnect ev->handle ox3f3
<70506.422403> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<70509.787475> ss-ipc: 185: do_disconnect ev->handle ox3f5
<70509.787510> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<70509.788396> ss-ipc: 185: do_disconnect ev->handle ox3f5
<70509.788424> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<70509.789518> ss-ipc: 185: do_disconnect ev->handle ox3f5
<70509.789544> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<70509.789582> ss-ipc: 185: do_disconnect ev->handle ox3f3
<70509.789604> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<70796.292393> ss-ipc: 185: do_disconnect ev->handle ox3f5
<70796.292427> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<70796.293012> ss-ipc: 185: do_disconnect ev->handle ox3f5
<70796.293038> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<70796.294099> ss-ipc: 185: do_disconnect ev->handle ox3f5
<70796.294129> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<70796.294169> ss-ipc: 185: do_disconnect ev->handle ox3f3
<70796.294191> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<70799.678177> ss-ipc: 185: do_disconnect ev->handle ox3f5
<70799.678213> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<70799.678798> ss-ipc: 185: do_disconnect ev->handle ox3f5
<70799.678823> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<70799.679875> ss-ipc: 185: do_disconnect ev->handle ox3f5
<70799.679901> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<70799.679939> ss-ipc: 185: do_disconnect ev->handle ox3f3
<70799.679961> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<71095.821934> ss-ipc: 185: do_disconnect ev->handle ox3f5
<71095.821950> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<71095.822184> ss-ipc: 185: do_disconnect ev->handle ox3f5
<71095.822194> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<71095.822661> ss-ipc: 185: do_disconnect ev->handle ox3f5
<71095.822672> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<71095.822688> ss-ipc: 185: do_disconnect ev->handle ox3f3
<71095.822696> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<71100.958763> ss-ipc: 185: do_disconnect ev->handle ox3f5
<71100.958779> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<71100.959013> ss-ipc: 185: do_disconnect ev->handle ox3f5
<71100.959023> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<71100.959476> ss-ipc: 185: do_disconnect ev->handle ox3f5
<71100.959487> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<71100.959503> ss-ipc: 185: do_disconnect ev->handle ox3f3
<71100.959512> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<71741.633753> ss-ipc: 185: do_disconnect ev->handle ox3f5
<71741.633777> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<71741.634370> ss-ipc: 185: do_disconnect ev->handle ox3f5
<71741.634388> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<71741.635155> ss-ipc: 185: do_disconnect ev->handle ox3f5
<71741.635173> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<71741.635201> ss-ipc: 185: do_disconnect ev->handle ox3f3
<71741.635217> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<71746.643955> ss-ipc: 185: do_disconnect ev->handle ox3f5
<71746.643991> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<71746.644558> ss-ipc: 185: do_disconnect ev->handle ox3f5
<71746.644586> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<71746.645660> ss-ipc: 185: do_disconnect ev->handle ox3f5
<71746.645688> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<71746.645728> ss-ipc: 185: do_disconnect ev->handle ox3f3
<71746.645753> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<75002.321842> ss-ipc: 185: do_disconnect ev->handle ox3f5
<75002.321861> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<75002.322138> ss-ipc: 185: do_disconnect ev->handle ox3f5
<75002.322150> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<75002.322766> ss-ipc: 185: do_disconnect ev->handle ox3f5
<75002.322780> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<75002.322803> ss-ipc: 185: do_disconnect ev->handle ox3f3
<75002.322814> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<75002.395755> ss-ipc: 185: do_disconnect ev->handle ox3f5
<75002.395783> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<75002.396248> ss-ipc: 185: do_disconnect ev->handle ox3f5
<75002.396269> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<75002.397072> ss-ipc: 185: do_disconnect ev->handle ox3f5
<75002.397085> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<75002.397106> ss-ipc: 185: do_disconnect ev->handle ox3f3
<75002.397118> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<85963.788534> ss-ipc: 185: do_disconnect ev->handle ox3f5
<85963.788556> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<85963.788829> ss-ipc: 185: do_disconnect ev->handle ox3f5
<85963.788842> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<85963.789409> ss-ipc: 185: do_disconnect ev->handle ox3f5
<85963.789423> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<85963.789446> ss-ipc: 185: do_disconnect ev->handle ox3f3
<85963.789455> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<85963.856627> ss-ipc: 185: do_disconnect ev->handle ox3f5
<85963.856645> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<85963.856896> ss-ipc: 185: do_disconnect ev->handle ox3f5
<85963.856907> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<85963.857371> ss-ipc: 185: do_disconnect ev->handle ox3f5
<85963.857383> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<85963.857401> ss-ipc: 185: do_disconnect ev->handle ox3f3
<85963.857410> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<98481.168467> ss-ipc: 185: do_disconnect ev->handle ox3f5
<98481.168490> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<98481.168845> ss-ipc: 185: do_disconnect ev->handle ox3f5
<98481.168860> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<98481.169626> ss-ipc: 185: do_disconnect ev->handle ox3f5
<98481.169640> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<98481.169660> ss-ipc: 185: do_disconnect ev->handle ox3f3
<98481.169671> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<98481.231979> ss-ipc: 185: do_disconnect ev->handle ox3f5
<98481.231999> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<98481.232304> ss-ipc: 185: do_disconnect ev->handle ox3f5
<98481.232317> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<98481.233088> ss-ipc: 185: do_disconnect ev->handle ox3f5
<98481.233101> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<98481.233122> ss-ipc: 185: do_disconnect ev->handle ox3f3
<98481.233132> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<101103.168326> ss-ipc: 185: do_disconnect ev->handle ox3f5
<101103.168347> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<101103.168814> ss-ipc: 185: do_disconnect ev->handle ox3f5
<101103.168830> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<101103.169573> ss-ipc: 185: do_disconnect ev->handle ox3f5
<101103.169587> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<101103.169608> ss-ipc: 185: do_disconnect ev->handle ox3f3
<101103.169619> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<101103.235557> ss-ipc: 185: do_disconnect ev->handle ox3f5
<101103.235575> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<101103.235810> ss-ipc: 185: do_disconnect ev->handle ox3f5
<101103.235821> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<101103.236273> ss-ipc: 185: do_disconnect ev->handle ox3f5
<101103.236283> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<101103.236299> ss-ipc: 185: do_disconnect ev->handle ox3f3
<101103.236308> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<122105.253653> ss-ipc: 185: do_disconnect ev->handle ox3f5
<122105.253670> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<122105.253877> ss-ipc: 185: do_disconnect ev->handle ox3f5
<122105.253889> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<122105.254412> ss-ipc: 185: do_disconnect ev->handle ox3f5
<122105.254427> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<122105.254448> ss-ipc: 185: do_disconnect ev->handle ox3f3
<122105.254461> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<122105.315267> ss-ipc: 185: do_disconnect ev->handle ox3f5
<122105.315284> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<122105.315520> ss-ipc: 185: do_disconnect ev->handle ox3f5
<122105.315530> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<122105.316158> ss-ipc: 185: do_disconnect ev->handle ox3f5
<122105.316170> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<122105.316192> ss-ipc: 185: do_disconnect ev->handle ox3f3
<122105.316200> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<126646.348581> ss-ipc: 185: do_disconnect ev->handle ox3f5
<126646.348616> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<126646.349405> ss-ipc: 185: do_disconnect ev->handle ox3f5
<126646.349432> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<126646.350490> ss-ipc: 185: do_disconnect ev->handle ox3f5
<126646.350516> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<126646.350556> ss-ipc: 185: do_disconnect ev->handle ox3f3
<126646.350579> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<126646.414289> ss-ipc: 185: do_disconnect ev->handle ox3f5
<126646.414306> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<126646.414537> ss-ipc: 185: do_disconnect ev->handle ox3f5
<126646.414548> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<126646.414990> ss-ipc: 185: do_disconnect ev->handle ox3f5
<126646.415001> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<126646.415016> ss-ipc: 185: do_disconnect ev->handle ox3f3
<126646.415025> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<130402.486334> ss-ipc: 185: do_disconnect ev->handle ox3f5
<130402.486350> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<130402.486883> ss-ipc: 185: do_disconnect ev->handle ox3f5
<130402.486907> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<130402.487834> ss-ipc: 185: do_disconnect ev->handle ox3f5
<130402.487857> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<130402.487892> ss-ipc: 185: do_disconnect ev->handle ox3f3
<130402.487912> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<130407.492709> ss-ipc: 185: do_disconnect ev->handle ox3f5
<130407.492737> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<130407.493280> ss-ipc: 185: do_disconnect ev->handle ox3f5
<130407.493299> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<130407.494115> ss-ipc: 185: do_disconnect ev->handle ox3f5
<130407.494136> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<130407.494162> ss-ipc: 185: do_disconnect ev->handle ox3f3
<130407.494176> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<130753.017621> ss-ipc: 185: do_disconnect ev->handle ox3f5
<130753.017638> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<130753.017873> ss-ipc: 185: do_disconnect ev->handle ox3f5
<130753.017883> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<130753.018337> ss-ipc: 185: do_disconnect ev->handle ox3f5
<130753.018348> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<130753.018365> ss-ipc: 185: do_disconnect ev->handle ox3f3
<130753.018373> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<130756.334018> ss-ipc: 185: do_disconnect ev->handle ox3f5
<130756.334035> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<130756.334277> ss-ipc: 185: do_disconnect ev->handle ox3f5
<130756.334287> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<130756.334729> ss-ipc: 185: do_disconnect ev->handle ox3f5
<130756.334739> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<130756.334758> ss-ipc: 185: do_disconnect ev->handle ox3f3
<130756.334767> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<131836.352730> ss-ipc: 185: do_disconnect ev->handle ox3f5
<131836.352760> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<131836.353225> ss-ipc: 185: do_disconnect ev->handle ox3f5
<131836.353246> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<131836.354135> ss-ipc: 185: do_disconnect ev->handle ox3f5
<131836.354157> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<131836.354190> ss-ipc: 185: do_disconnect ev->handle ox3f3
<131836.354208> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<131839.723190> ss-ipc: 185: do_disconnect ev->handle ox3f5
<131839.723209> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<131839.723476> ss-ipc: 185: do_disconnect ev->handle ox3f5
<131839.723488> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<131839.723997> ss-ipc: 185: do_disconnect ev->handle ox3f5
<131839.724009> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<131839.724027> ss-ipc: 185: do_disconnect ev->handle ox3f3
<131839.724037> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<132380.792282> ss-ipc: 185: do_disconnect ev->handle ox3f5
<132380.792317> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<132380.792893> ss-ipc: 185: do_disconnect ev->handle ox3f5
<132380.792926> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<132380.794280> ss-ipc: 185: do_disconnect ev->handle ox3f5
<132380.794307> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<132380.794351> ss-ipc: 185: do_disconnect ev->handle ox3f3
<132380.794374> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<132384.176076> ss-ipc: 185: do_disconnect ev->handle ox3f5
<132384.176093> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<132384.176336> ss-ipc: 185: do_disconnect ev->handle ox3f5
<132384.176347> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<132384.176798> ss-ipc: 185: do_disconnect ev->handle ox3f5
<132384.176811> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<132384.176830> ss-ipc: 185: do_disconnect ev->handle ox3f3
<132384.176839> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
